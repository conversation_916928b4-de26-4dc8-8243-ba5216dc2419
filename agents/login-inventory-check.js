
module.exports = async ({ tools }) => {
  // 1. Start browser and go to login page
  await tools.playwright-mcp_init_browser({ url: "https://www.saucedemo.com/v1/" });

  // 2. Fill in username and password, click login, wait for inventory page
  const result = await tools.playwright-mcp_execute_code({
    code: `
      async function run(page) {
        await page.fill('#user-name', 'standard_user');
        await page.fill('#password', 'secret_sauce');
        await page.click('#login-button');
        await page.waitForURL('**/inventory*', { timeout: 10000 });
        const url = page.url();
        const productsVisible = await page.isVisible('.inventory_list');
        const menuVisible = await page.isVisible('#menu_button_container');
        const logoutVisible = await page.isVisible('#logout_sidebar_link');
        return { url, productsVisible, menuVisible, logoutVisible };
      }
    `
  });

  // 3. Return results
  return {
    success: result.productsVisible && result.menuVisible && result.logoutVisible,
    details: result
  };
};
