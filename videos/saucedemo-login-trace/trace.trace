{"version":8,"type":"context-options","origin":"library","browserName":"chromium","options":{"noDefaultViewport":true,"bypassCSP":true,"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","selectorEngines":[],"acceptDownloads":"accept"},"platform":"darwin","wallTime":1755287564732,"monotonicTime":197805.785,"sdkLanguage":"javascript","testIdAttributeName":"data-testid","contextId":"browser-context@fe38e5172401845f22ed4897933514a6"}
{"type":"screencast-frame","pageId":"page@66ecceb74b017ecb20d1b87d48fb4ceb","sha1":"<EMAIL>","width":1200,"height":894,"timestamp":197851.352,"frameSwapWallTime":1755287564760.5122}
{"type":"before","callId":"call@62","startTime":197855.559,"class":"Frame","method":"fill","params":{"selector":"#user-name","value":"standard_user","timeout":30000},"pageId":"page@66ecceb74b017ecb20d1b87d48fb4ceb","beforeSnapshot":"before@call@62"}
{"type":"frame-snapshot","snapshot":{"callId":"call@62","snapshotName":"before@call@62","pageId":"page@66ecceb74b017ecb20d1b87d48fb4ceb","frameId":"frame@9b484cf0b4c527045bd2101a2ffc781f","frameUrl":"https://www.saucedemo.com/v1/","doctype":"html","html":["HTML",{},["HEAD",{},["BASE",{"href":"https://www.saucedemo.com/v1/"}],"\n    ",["META",{"charset":"utf-8"}],"\n    ",["TITLE",{},"Swag Labs"],"\n    ",["META",{"name":"viewport","content":"width=device-width, initial-scale=1, maximum-scale=1"}],"\n    ",["LINK",{"rel":"stylesheet","type":"text/css","href":"css/sample-app-web.css"}],"\n    ",["LINK",{"rel":"icon","type":"image/png","href":"favicon.ico"}],"\n"],"\n",["BODY",{"class":"main-body"},"\n\n",["DIV",{"class":"login_logo"}],"\n\n",["DIV",{"class":"login_wrapper"},"\n\n    ",["DIV",{"class":"login_wrapper-inner"},"\n\n        ",["DIV",{"id":"login_button_container","class":"form_column"},["DIV",{"class":"login-box"},["FORM",{},["INPUT",{"__playwright_value_":"","type":"text","class":"form_input","data-test":"username","id":"user-name","name":"user-name","placeholder":"Username","autocorrect":"off","autocapitalize":"none","value":""}],["INPUT",{"__playwright_value_":"","type":"password","class":"form_input","data-test":"password","id":"password","name":"password","placeholder":"Password","autocorrect":"off","autocapitalize":"none","value":""}],["INPUT",{"__playwright_value_":"LOGIN","type":"submit","class":"btn_action","id":"login-button","value":"LOGIN"}],""]]],"\n\n        ",["IMG",{"__playwright_current_src__":"https://www.saucedemo.com/v1/img/Login_Bot_graphic.png","class":"bot_column","src":"img/Login_Bot_graphic.png"}],"\n\n    "],"\n    ",["DIV",{"class":"login_credentials_wrap"},"\n        ",["DIV",{"class":"login_credentials_wrap-inner"},"\n            ",["DIV",{"id":"login_credentials","class":"login_credentials"},"\n                ",["H4",{},"Accepted usernames are:"],"\n                standard_user",["BR"],"\n                locked_out_user",["BR"],"\n                problem_user",["BR"],"\n                performance_glitch_user",["BR"],"\n\n            "],"\n            ",["DIV",{"class":"login_password"},"\n                ",["H4",{},"Password for all users:"],"\n                secret_sauce\n            "],"\n        "],"\n    "],"\n"],"\n","\n\n",["DIV",{"id":"mcp-sidebar","style":"position: fixed; top: 0px; right: 0px; width: 500px; height: 100vh; background: rgb(245, 245, 245); border-left: 1px solid rgb(228, 228, 231); z-index: 999999; display: flex; flex-direction: column; overflow: hidden; transition: transform 0.3s;"},["DIV",{"id":"mcp-resize-handle","style":"position: absolute; left: 0px; top: 0px; width: 4px; height: 100%; cursor: ew-resize; background: transparent;"}],["IFRAME",{"__playwright_bounding_rect__":"{\"left\":700,\"top\":0,\"right\":1200,\"bottom\":894}","src":"/snapshot/frame@ce4c039a75a8116e0b6b33630d6a8a57","name":"toolbox-frame","style":"width: 100%; height: 100%; border: none;"}]],["BUTTON",{"id":"mcp-sidebar-toggle-button","data-skip-recording":"true","style":"position: fixed; right: 500px; top: 50%; transform: translateY(-50%); background: rgb(245, 245, 245); border-top: 1px solid rgb(228, 228, 231); border-bottom: 1px solid rgb(228, 228, 231); border-left: 1px solid rgb(228, 228, 231); border-image: initial; border-right: none; border-radius: 4px 0px 0px 4px; font-size: 20px; cursor: pointer; padding: 8px; color: rgb(17, 24, 39); z-index: 999999; transition: right 0.3s;"},"⟩"]]],"viewport":{"width":1200,"height":894},"timestamp":197858.485,"wallTime":1755287564784,"collectionTime":1.5999999046325684,"resourceOverrides":[],"isMainFrame":true}}
{"type":"frame-snapshot","snapshot":{"callId":"call@62","snapshotName":"before@call@62","pageId":"page@66ecceb74b017ecb20d1b87d48fb4ceb","frameId":"frame@ce4c039a75a8116e0b6b33630d6a8a57","frameUrl":"http://localhost:5174/","doctype":"html","html":["HTML",{"lang":"en"},["HEAD",{},["BASE",{"href":"http://localhost:5174/"}],"\n    ",["META",{"charset":"utf-8"}],"\n    ",["LINK",{"rel":"icon","type":"image/svg+xml","href":"/vite.svg"}],"\n    ",["META",{"name":"viewport","content":"width=device-width, initial-scale=1.0"}],"\n    ",["TITLE",{},"Vite + React + TS"],"\n    ","\n    ",["LINK",{"rel":"stylesheet","crossorigin":"","href":"/assets/index-Dqrek7rF.css"}],"\n  "],"\n  ",["BODY",{},"\n    ",["DIV",{"id":"root"},["DIV",{"class":"fixed top-0 right-0 w-full h-screen bg-gray-100 border-l border-zinc-200 z-[999999] flex flex-col overflow-hidden"},["DIV",{"class":"p-4 bg-white border-b border-zinc-200 flex items-center justify-center"},["H3",{"class":"m-0 text-base font-medium text-gray-900"},"Playwright MCP"]],["DIV",{"dir":"ltr","data-orientation":"horizontal","data-slot":"tabs","class":"gap-2 flex-1 flex flex-col"},["DIV",{"class":"p-4 bg-white border-b border-zinc-200"},["DIV",{"role":"tablist","aria-orientation":"horizontal","data-slot":"tabs-list","class":"bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]","tabindex":"0","data-orientation":"horizontal","style":"outline: none;"},["BUTTON",{"type":"button","role":"tab","aria-selected":"true","aria-controls":"radix-:r0:-content-context","data-state":"active","id":"radix-:r0:-trigger-context","data-slot":"tabs-trigger","class":"data-[state=active]:bg-background data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/50 inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4","tabindex":"-1","data-orientation":"horizontal","data-radix-collection-item":""},"Context"],["BUTTON",{"type":"button","role":"tab","aria-selected":"false","aria-controls":"radix-:r0:-content-execute","data-state":"inactive","id":"radix-:r0:-trigger-execute","data-slot":"tabs-trigger","class":"data-[state=active]:bg-background data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/50 inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4","tabindex":"-1","data-orientation":"horizontal","data-radix-collection-item":""},"Execute"]]],["DIV",{"data-state":"active","data-orientation":"horizontal","role":"tabpanel","aria-labelledby":"radix-:r0:-trigger-context","id":"radix-:r0:-content-context","tabindex":"0","data-slot":"tabs-content","class":"outline-none flex-1","style":"animation-duration: 0s;"},["DIV",{"class":"flex-1 flex flex-col h-full bg-white"},["DIV",{"class":"p-4 flex gap-2"},["BUTTON",{"data-slot":"button","class":"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive bg-primary text-primary-foreground shadow-xs hover:bg-primary/90 h-9 px-4 py-2 has-[>svg]:px-3 w-40"},["DIV",{"class":"flex items-center gap-2"},["DIV",{"class":"w-4 h-4 rounded-full bg-red-500"}],"Start Recording"]]],["DIV",{"dir":"ltr","data-slot":"scroll-area","class":"relative flex-1 max-h-[calc(100vh-194px)] overflow-y-auto","style":"position: relative; --radix-scroll-area-corner-width: 0px; --radix-scroll-area-corner-height: 0px;"},["STYLE",{},"[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}"],["DIV",{"data-radix-scroll-area-viewport":"","data-slot":"scroll-area-viewport","class":"ring-ring/10 dark:ring-ring/20 dark:outline-ring/40 outline-ring/50 size-full rounded-[inherit] transition-[color,box-shadow] focus-visible:ring-4 focus-visible:outline-1","style":"overflow: hidden scroll;"},["DIV",{"style":"min-width: 100%; display: table;"},["DIV",{"class":"flex flex-col gap-2 p-4"},["DIV",{"class":"text-sm text-muted-foreground"},"No interactions recorded yet!",["BR"],["BR"],"Click 'Start Recording' to record interactions.",["BR"],["BR"],"Once you are done with it, go to your MCP server (like Claude, Cursor), ask it to pull context using `get-context` tool and give it instructions on what kind of testcase to write ."]]]]]]],["DIV",{"data-state":"inactive","data-orientation":"horizontal","role":"tabpanel","aria-labelledby":"radix-:r0:-trigger-execute","hidden":"","id":"radix-:r0:-content-execute","tabindex":"0","data-slot":"tabs-content","class":"outline-none flex-1"}]]]],"\n    ","\n  \n\n"]],"viewport":{"width":500,"height":894},"timestamp":197859.037,"wallTime":1755287564784,"collectionTime":1.5,"resourceOverrides":[],"isMainFrame":false}}
{"type":"log","callId":"call@62","time":197859.676,"message":"waiting for locator('#user-name')"}
{"type":"log","callId":"call@62","time":197868.534,"message":"  locator resolved to <input value=\"\" type=\"text\" id=\"user-name\" name=\"user-name\" autocorrect=\"off\" class=\"form_input\" data-test=\"username\" autocapitalize=\"none\" placeholder=\"Username\"/>"}
{"type":"log","callId":"call@62","time":197869.022,"message":"  fill(\"standard_user\")"}
{"type":"log","callId":"call@62","time":197869.031,"message":"attempting fill action"}
{"type":"input","callId":"call@62","inputSnapshot":"input@call@62"}
{"type":"frame-snapshot","snapshot":{"callId":"call@62","snapshotName":"input@call@62","pageId":"page@66ecceb74b017ecb20d1b87d48fb4ceb","frameId":"frame@9b484cf0b4c527045bd2101a2ffc781f","frameUrl":"https://www.saucedemo.com/v1/","doctype":"html","html":["HTML",{},[[1,13]],[[1,14]],["BODY",{"class":"main-body"},[[1,15]],[[1,16]],[[1,17]],["DIV",{"class":"login_wrapper"},[[1,18]],["DIV",{"class":"login_wrapper-inner"},[[1,19]],["DIV",{"id":"login_button_container","class":"form_column"},["DIV",{"class":"login-box"},["FORM",{},["INPUT",{"__playwright_value_":"","__playwright_target__":"call@62","type":"text","class":"form_input","data-test":"username","id":"user-name","name":"user-name","placeholder":"Username","autocorrect":"off","autocapitalize":"none","value":""}],[[1,21]],[[1,22]],[[1,23]]]]],[[1,27]],[[1,28]],[[1,29]]],[[1,31]],[[1,56]],[[1,57]]],[[1,59]],[[1,60]],[[1,63]],[[1,65]]]],"viewport":{"width":1200,"height":894},"timestamp":197869.767,"wallTime":1755287564795,"collectionTime":0.2999999523162842,"resourceOverrides":[],"isMainFrame":true}}
{"type":"frame-snapshot","snapshot":{"callId":"call@62","snapshotName":"input@call@62","pageId":"page@66ecceb74b017ecb20d1b87d48fb4ceb","frameId":"frame@ce4c039a75a8116e0b6b33630d6a8a57","frameUrl":"http://localhost:5174/","doctype":"html","html":[[1,54]],"viewport":{"width":500,"height":894},"timestamp":197869.9,"wallTime":1755287564796,"collectionTime":0.19999980926513672,"resourceOverrides":[],"isMainFrame":false}}
{"type":"log","callId":"call@62","time":197869.948,"message":"  waiting for element to be visible, enabled and editable"}
{"type":"after","callId":"call@62","endTime":197874.143,"afterSnapshot":"after@call@62"}
{"type":"frame-snapshot","snapshot":{"callId":"call@62","snapshotName":"after@call@62","pageId":"page@66ecceb74b017ecb20d1b87d48fb4ceb","frameId":"frame@9b484cf0b4c527045bd2101a2ffc781f","frameUrl":"https://www.saucedemo.com/v1/","doctype":"html","html":["HTML",{},[[2,13]],[[2,14]],["BODY",{"class":"main-body"},[[2,15]],[[2,16]],[[2,17]],["DIV",{"class":"login_wrapper"},[[2,18]],["DIV",{"class":"login_wrapper-inner"},[[2,19]],["DIV",{"id":"login_button_container","class":"form_column"},["DIV",{"class":"login-box"},["FORM",{},["INPUT",{"__playwright_value_":"standard_user","__playwright_target__":"call@62","type":"text","class":"form_input","data-test":"username","id":"user-name","name":"user-name","placeholder":"Username","autocorrect":"off","autocapitalize":"none","value":"standard_user"}],[[2,21]],[[2,22]],[[2,23]]]]],[[2,27]],[[2,28]],[[2,29]]],[[2,31]],[[2,56]],[[2,57]]],[[2,59]],[[2,60]],[[2,63]],[[2,65]]]],"viewport":{"width":1200,"height":894},"timestamp":197875.158,"wallTime":1755287564801,"collectionTime":0.20000004768371582,"resourceOverrides":[],"isMainFrame":true}}
{"type":"frame-snapshot","snapshot":{"callId":"call@62","snapshotName":"after@call@62","pageId":"page@66ecceb74b017ecb20d1b87d48fb4ceb","frameId":"frame@ce4c039a75a8116e0b6b33630d6a8a57","frameUrl":"http://localhost:5174/","doctype":"html","html":[[2,54]],"viewport":{"width":500,"height":894},"timestamp":197875.228,"wallTime":1755287564801,"collectionTime":0.20000004768371582,"resourceOverrides":[],"isMainFrame":false}}
{"type":"before","callId":"call@64","startTime":197875.964,"class":"Frame","method":"fill","params":{"selector":"#password","value":"secret_sauce","timeout":30000},"pageId":"page@66ecceb74b017ecb20d1b87d48fb4ceb","beforeSnapshot":"before@call@64"}
{"type":"frame-snapshot","snapshot":{"callId":"call@64","snapshotName":"before@call@64","pageId":"page@66ecceb74b017ecb20d1b87d48fb4ceb","frameId":"frame@9b484cf0b4c527045bd2101a2ffc781f","frameUrl":"https://www.saucedemo.com/v1/","doctype":"html","html":[[1,7]],"viewport":{"width":1200,"height":894},"timestamp":197876.761,"wallTime":1755287564802,"collectionTime":0.20000004768371582,"resourceOverrides":[],"isMainFrame":true}}
{"type":"frame-snapshot","snapshot":{"callId":"call@64","snapshotName":"before@call@64","pageId":"page@66ecceb74b017ecb20d1b87d48fb4ceb","frameId":"frame@ce4c039a75a8116e0b6b33630d6a8a57","frameUrl":"http://localhost:5174/","doctype":"html","html":[[3,54]],"viewport":{"width":500,"height":894},"timestamp":197877.205,"wallTime":1755287564802,"collectionTime":0.20000004768371582,"resourceOverrides":[],"isMainFrame":false}}
{"type":"log","callId":"call@64","time":197877.654,"message":"waiting for locator('#password')"}
{"type":"screencast-frame","pageId":"page@66ecceb74b017ecb20d1b87d48fb4ceb","sha1":"<EMAIL>","width":1200,"height":894,"timestamp":197878.552,"frameSwapWallTime":1755287564801.392}
{"type":"log","callId":"call@64","time":197880.17,"message":"  locator resolved to <input value=\"\" id=\"password\" type=\"password\" name=\"password\" autocorrect=\"off\" class=\"form_input\" data-test=\"password\" autocapitalize=\"none\" placeholder=\"Password\"/>"}
{"type":"log","callId":"call@64","time":197880.959,"message":"  fill(\"secret_sauce\")"}
{"type":"log","callId":"call@64","time":197880.968,"message":"attempting fill action"}
{"type":"input","callId":"call@64","inputSnapshot":"input@call@64"}
{"type":"frame-snapshot","snapshot":{"callId":"call@64","snapshotName":"input@call@64","pageId":"page@66ecceb74b017ecb20d1b87d48fb4ceb","frameId":"frame@9b484cf0b4c527045bd2101a2ffc781f","frameUrl":"https://www.saucedemo.com/v1/","doctype":"html","html":["HTML",{},[[4,13]],[[4,14]],["BODY",{"class":"main-body"},[[4,15]],[[4,16]],[[4,17]],["DIV",{"class":"login_wrapper"},[[4,18]],["DIV",{"class":"login_wrapper-inner"},[[4,19]],["DIV",{"id":"login_button_container","class":"form_column"},["DIV",{"class":"login-box"},["FORM",{},[[2,0]],["INPUT",{"__playwright_value_":"","__playwright_target__":"call@64","type":"password","class":"form_input","data-test":"password","id":"password","name":"password","placeholder":"Password","autocorrect":"off","autocapitalize":"none","value":""}],[[4,22]],[[4,23]]]]],[[4,27]],[[4,28]],[[4,29]]],[[4,31]],[[4,56]],[[4,57]]],[[4,59]],[[4,60]],[[4,63]],[[4,65]]]],"viewport":{"width":1200,"height":894},"timestamp":197882.21,"wallTime":1755287564807,"collectionTime":0.20000004768371582,"resourceOverrides":[],"isMainFrame":true}}
{"type":"frame-snapshot","snapshot":{"callId":"call@64","snapshotName":"input@call@64","pageId":"page@66ecceb74b017ecb20d1b87d48fb4ceb","frameId":"frame@ce4c039a75a8116e0b6b33630d6a8a57","frameUrl":"http://localhost:5174/","doctype":"html","html":[[4,54]],"viewport":{"width":500,"height":894},"timestamp":197882.388,"wallTime":1755287564807,"collectionTime":0.20000004768371582,"resourceOverrides":[],"isMainFrame":false}}
{"type":"log","callId":"call@64","time":197882.432,"message":"  waiting for element to be visible, enabled and editable"}
{"type":"screencast-frame","pageId":"page@66ecceb74b017ecb20d1b87d48fb4ceb","sha1":"<EMAIL>","width":1200,"height":894,"timestamp":197887.565,"frameSwapWallTime":1755287564810.731}
{"type":"after","callId":"call@64","endTime":197924.054,"afterSnapshot":"after@call@64"}
{"type":"frame-snapshot","snapshot":{"callId":"call@64","snapshotName":"after@call@64","pageId":"page@66ecceb74b017ecb20d1b87d48fb4ceb","frameId":"frame@9b484cf0b4c527045bd2101a2ffc781f","frameUrl":"https://www.saucedemo.com/v1/","doctype":"html","html":["HTML",{},[[5,13]],[[5,14]],["BODY",{"class":"main-body"},[[5,15]],[[5,16]],[[5,17]],["DIV",{"class":"login_wrapper"},[[5,18]],["DIV",{"class":"login_wrapper-inner"},[[5,19]],["DIV",{"id":"login_button_container","class":"form_column"},["DIV",{"class":"login-box"},["FORM",{},[[3,0]],["INPUT",{"__playwright_value_":"secret_sauce","__playwright_target__":"call@64","type":"password","class":"form_input","data-test":"password","id":"password","name":"password","placeholder":"Password","autocorrect":"off","autocapitalize":"none","value":"secret_sauce"}],[[5,22]],[[5,23]]]]],[[5,27]],[[5,28]],[[5,29]]],[[5,31]],[[5,56]],[[5,57]]],[[5,59]],[[5,60]],[[5,63]],[[5,65]]]],"viewport":{"width":1200,"height":894},"timestamp":197925.052,"wallTime":1755287564851,"collectionTime":0.2999999523162842,"resourceOverrides":[],"isMainFrame":true}}
{"type":"frame-snapshot","snapshot":{"callId":"call@64","snapshotName":"after@call@64","pageId":"page@66ecceb74b017ecb20d1b87d48fb4ceb","frameId":"frame@ce4c039a75a8116e0b6b33630d6a8a57","frameUrl":"http://localhost:5174/","doctype":"html","html":[[5,54]],"viewport":{"width":500,"height":894},"timestamp":197925.209,"wallTime":1755287564851,"collectionTime":0.2999999523162842,"resourceOverrides":[],"isMainFrame":false}}
{"type":"before","callId":"call@66","startTime":197925.666,"class":"Frame","method":"click","params":{"selector":"#login-button","timeout":30000},"pageId":"page@66ecceb74b017ecb20d1b87d48fb4ceb","beforeSnapshot":"before@call@66"}
{"type":"frame-snapshot","snapshot":{"callId":"call@66","snapshotName":"before@call@66","pageId":"page@66ecceb74b017ecb20d1b87d48fb4ceb","frameId":"frame@ce4c039a75a8116e0b6b33630d6a8a57","frameUrl":"http://localhost:5174/","doctype":"html","html":[[6,54]],"viewport":{"width":500,"height":894},"timestamp":197926.714,"wallTime":1755287564852,"collectionTime":0.10000014305114746,"resourceOverrides":[],"isMainFrame":false}}
{"type":"frame-snapshot","snapshot":{"callId":"call@66","snapshotName":"before@call@66","pageId":"page@66ecceb74b017ecb20d1b87d48fb4ceb","frameId":"frame@9b484cf0b4c527045bd2101a2ffc781f","frameUrl":"https://www.saucedemo.com/v1/","doctype":"html","html":[[1,7]],"viewport":{"width":1200,"height":894},"timestamp":197926.872,"wallTime":1755287564852,"collectionTime":0.19999980926513672,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@66","time":197927.273,"message":"waiting for locator('#login-button')"}
{"type":"screencast-frame","pageId":"page@66ecceb74b017ecb20d1b87d48fb4ceb","sha1":"<EMAIL>","width":1200,"height":894,"timestamp":197928.806,"frameSwapWallTime":1755287564852.481}
{"type":"log","callId":"call@66","time":197928.888,"message":"  locator resolved to <input type=\"submit\" value=\"LOGIN\" id=\"login-button\" class=\"btn_action\"/>"}
{"type":"log","callId":"call@66","time":197929.249,"message":"attempting click action"}
{"type":"log","callId":"call@66","time":197929.281,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"screencast-frame","pageId":"page@66ecceb74b017ecb20d1b87d48fb4ceb","sha1":"<EMAIL>","width":1200,"height":894,"timestamp":197931.653,"frameSwapWallTime":1755287564855.615}
{"type":"log","callId":"call@66","time":197959.435,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@66","time":197959.449,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@66","time":197959.615,"message":"  done scrolling"}
{"type":"input","callId":"call@66","point":{"x":285.96,"y":323},"inputSnapshot":"input@call@66"}
{"type":"frame-snapshot","snapshot":{"callId":"call@66","snapshotName":"input@call@66","pageId":"page@66ecceb74b017ecb20d1b87d48fb4ceb","frameId":"frame@9b484cf0b4c527045bd2101a2ffc781f","frameUrl":"https://www.saucedemo.com/v1/","doctype":"html","html":["HTML",{},[[7,13]],[[7,14]],["BODY",{"class":"main-body"},[[7,15]],[[7,16]],[[7,17]],["DIV",{"class":"login_wrapper"},[[7,18]],["DIV",{"class":"login_wrapper-inner"},[[7,19]],["DIV",{"id":"login_button_container","class":"form_column"},["DIV",{"class":"login-box"},["FORM",{},[[5,0]],[[2,0]],["INPUT",{"__playwright_value_":"LOGIN","__playwright_target__":"call@66","type":"submit","class":"btn_action","id":"login-button","value":"LOGIN"}],[[7,23]]]]],[[7,27]],[[7,28]],[[7,29]]],[[7,31]],[[7,56]],[[7,57]]],[[7,59]],[[7,60]],[[7,63]],[[7,65]]]],"viewport":{"width":1200,"height":894},"timestamp":197960.964,"wallTime":1755287564886,"collectionTime":0.20000004768371582,"resourceOverrides":[],"isMainFrame":true}}
{"type":"frame-snapshot","snapshot":{"callId":"call@66","snapshotName":"input@call@66","pageId":"page@66ecceb74b017ecb20d1b87d48fb4ceb","frameId":"frame@ce4c039a75a8116e0b6b33630d6a8a57","frameUrl":"http://localhost:5174/","doctype":"html","html":[[7,54]],"viewport":{"width":500,"height":894},"timestamp":197961.023,"wallTime":1755287564887,"collectionTime":0.09999990463256836,"resourceOverrides":[],"isMainFrame":false}}
{"type":"log","callId":"call@66","time":197961.875,"message":"  performing click action"}
{"type":"log","callId":"call@66","time":198034.845,"message":"  click action done"}
{"type":"log","callId":"call@66","time":198034.855,"message":"  waiting for scheduled navigations to finish"}
{"type":"screencast-frame","pageId":"page@66ecceb74b017ecb20d1b87d48fb4ceb","sha1":"<EMAIL>","width":1200,"height":894,"timestamp":198039.258,"frameSwapWallTime":1755287564961.123}
{"type":"log","callId":"call@66","time":198324.044,"message":"  navigated to \"https://www.saucedemo.com/v1/inventory.html\""}
{"type":"console","messageType":"log","text":"Recording initialized for window: https://www.saucedemo.com/v1/inventory.html","args":[{"preview":"Recording initialized for window:","value":"Recording initialized for window:"},{"preview":"https://www.saucedemo.com/v1/inventory.html","value":"https://www.saucedemo.com/v1/inventory.html"}],"location":{"url":"","lineNumber":196,"columnNumber":12},"time":198333.578,"pageId":"page@66ecceb74b017ecb20d1b87d48fb4ceb"}
{"type":"log","callId":"call@66","time":198355.648,"message":"  navigations have finished"}
{"type":"after","callId":"call@66","endTime":198355.758,"point":{"x":285.96,"y":323},"afterSnapshot":"after@call@66"}
{"type":"frame-snapshot","snapshot":{"callId":"call@66","snapshotName":"after@call@66","pageId":"page@66ecceb74b017ecb20d1b87d48fb4ceb","frameId":"frame@9b484cf0b4c527045bd2101a2ffc781f","frameUrl":"https://www.saucedemo.com/v1/inventory.html","doctype":"html","html":["HTML",{},["HEAD",{},["BASE",{"href":"https://www.saucedemo.com/v1/inventory.html"}],"\n    ",["META",{"charset":"utf-8"}],"\n    ",["META",{"name":"viewport","content":"width=device-width, initial-scale=1, maximum-scale=1"}],"\n    ",["TITLE",{},"Swag Labs"],"\n    ",["STYLE",{},"svg:not(:root).svg-inline--fa {\n  overflow: visible; }\n\n.svg-inline--fa {\n  display: inline-block;\n  font-size: inherit;\n  height: 1em;\n  overflow: visible;\n  vertical-align: -.125em; }\n  .svg-inline--fa.fa-lg {\n    vertical-align: -.225em; }\n  .svg-inline--fa.fa-w-1 {\n    width: 0.0625em; }\n  .svg-inline--fa.fa-w-2 {\n    width: 0.125em; }\n  .svg-inline--fa.fa-w-3 {\n    width: 0.1875em; }\n  .svg-inline--fa.fa-w-4 {\n    width: 0.25em; }\n  .svg-inline--fa.fa-w-5 {\n    width: 0.3125em; }\n  .svg-inline--fa.fa-w-6 {\n    width: 0.375em; }\n  .svg-inline--fa.fa-w-7 {\n    width: 0.4375em; }\n  .svg-inline--fa.fa-w-8 {\n    width: 0.5em; }\n  .svg-inline--fa.fa-w-9 {\n    width: 0.5625em; }\n  .svg-inline--fa.fa-w-10 {\n    width: 0.625em; }\n  .svg-inline--fa.fa-w-11 {\n    width: 0.6875em; }\n  .svg-inline--fa.fa-w-12 {\n    width: 0.75em; }\n  .svg-inline--fa.fa-w-13 {\n    width: 0.8125em; }\n  .svg-inline--fa.fa-w-14 {\n    width: 0.875em; }\n  .svg-inline--fa.fa-w-15 {\n    width: 0.9375em; }\n  .svg-inline--fa.fa-w-16 {\n    width: 1em; }\n  .svg-inline--fa.fa-w-17 {\n    width: 1.0625em; }\n  .svg-inline--fa.fa-w-18 {\n    width: 1.125em; }\n  .svg-inline--fa.fa-w-19 {\n    width: 1.1875em; }\n  .svg-inline--fa.fa-w-20 {\n    width: 1.25em; }\n  .svg-inline--fa.fa-pull-left {\n    margin-right: .3em;\n    width: auto; }\n  .svg-inline--fa.fa-pull-right {\n    margin-left: .3em;\n    width: auto; }\n  .svg-inline--fa.fa-border {\n    height: 1.5em; }\n  .svg-inline--fa.fa-li {\n    width: 2em; }\n  .svg-inline--fa.fa-fw {\n    width: 1.25em; }\n\n.fa-layers svg.svg-inline--fa {\n  bottom: 0;\n  left: 0;\n  margin: auto;\n  position: absolute;\n  right: 0;\n  top: 0; }\n\n.fa-layers {\n  display: inline-block;\n  height: 1em;\n  position: relative;\n  text-align: center;\n  vertical-align: -.125em;\n  width: 1em; }\n  .fa-layers svg.svg-inline--fa {\n    -webkit-transform-origin: center center;\n            transform-origin: center center; }\n\n.fa-layers-text, .fa-layers-counter {\n  display: inline-block;\n  position: absolute;\n  text-align: center; }\n\n.fa-layers-text {\n  left: 50%;\n  top: 50%;\n  -webkit-transform: translate(-50%, -50%);\n          transform: translate(-50%, -50%);\n  -webkit-transform-origin: center center;\n          transform-origin: center center; }\n\n.fa-layers-counter {\n  background-color: #ff253a;\n  border-radius: 1em;\n  -webkit-box-sizing: border-box;\n          box-sizing: border-box;\n  color: #fff;\n  height: 1.5em;\n  line-height: 1;\n  max-width: 5em;\n  min-width: 1.5em;\n  overflow: hidden;\n  padding: .25em;\n  right: 0;\n  text-overflow: ellipsis;\n  top: 0;\n  -webkit-transform: scale(0.25);\n          transform: scale(0.25);\n  -webkit-transform-origin: top right;\n          transform-origin: top right; }\n\n.fa-layers-bottom-right {\n  bottom: 0;\n  right: 0;\n  top: auto;\n  -webkit-transform: scale(0.25);\n          transform: scale(0.25);\n  -webkit-transform-origin: bottom right;\n          transform-origin: bottom right; }\n\n.fa-layers-bottom-left {\n  bottom: 0;\n  left: 0;\n  right: auto;\n  top: auto;\n  -webkit-transform: scale(0.25);\n          transform: scale(0.25);\n  -webkit-transform-origin: bottom left;\n          transform-origin: bottom left; }\n\n.fa-layers-top-right {\n  right: 0;\n  top: 0;\n  -webkit-transform: scale(0.25);\n          transform: scale(0.25);\n  -webkit-transform-origin: top right;\n          transform-origin: top right; }\n\n.fa-layers-top-left {\n  left: 0;\n  right: auto;\n  top: 0;\n  -webkit-transform: scale(0.25);\n          transform: scale(0.25);\n  -webkit-transform-origin: top left;\n          transform-origin: top left; }\n\n.fa-lg {\n  font-size: 1.33333em;\n  line-height: 0.75em;\n  vertical-align: -.0667em; }\n\n.fa-xs {\n  font-size: .75em; }\n\n.fa-sm {\n  font-size: .875em; }\n\n.fa-1x {\n  font-size: 1em; }\n\n.fa-2x {\n  font-size: 2em; }\n\n.fa-3x {\n  font-size: 3em; }\n\n.fa-4x {\n  font-size: 4em; }\n\n.fa-5x {\n  font-size: 5em; }\n\n.fa-6x {\n  font-size: 6em; }\n\n.fa-7x {\n  font-size: 7em; }\n\n.fa-8x {\n  font-size: 8em; }\n\n.fa-9x {\n  font-size: 9em; }\n\n.fa-10x {\n  font-size: 10em; }\n\n.fa-fw {\n  text-align: center;\n  width: 1.25em; }\n\n.fa-ul {\n  list-style-type: none;\n  margin-left: 2.5em;\n  padding-left: 0; }\n  .fa-ul > li {\n    position: relative; }\n\n.fa-li {\n  left: -2em;\n  position: absolute;\n  text-align: center;\n  width: 2em;\n  line-height: inherit; }\n\n.fa-border {\n  border: solid 0.08em #eee;\n  border-radius: .1em;\n  padding: .2em .25em .15em; }\n\n.fa-pull-left {\n  float: left; }\n\n.fa-pull-right {\n  float: right; }\n\n.fa.fa-pull-left,\n.fas.fa-pull-left,\n.far.fa-pull-left,\n.fal.fa-pull-left,\n.fab.fa-pull-left {\n  margin-right: .3em; }\n\n.fa.fa-pull-right,\n.fas.fa-pull-right,\n.far.fa-pull-right,\n.fal.fa-pull-right,\n.fab.fa-pull-right {\n  margin-left: .3em; }\n\n.fa-spin {\n  -webkit-animation: fa-spin 2s infinite linear;\n          animation: fa-spin 2s infinite linear; }\n\n.fa-pulse {\n  -webkit-animation: fa-spin 1s infinite steps(8);\n          animation: fa-spin 1s infinite steps(8); }\n\n@-webkit-keyframes fa-spin {\n  0% {\n    -webkit-transform: rotate(0deg);\n            transform: rotate(0deg); }\n  100% {\n    -webkit-transform: rotate(360deg);\n            transform: rotate(360deg); } }\n\n@keyframes fa-spin {\n  0% {\n    -webkit-transform: rotate(0deg);\n            transform: rotate(0deg); }\n  100% {\n    -webkit-transform: rotate(360deg);\n            transform: rotate(360deg); } }\n\n.fa-rotate-90 {\n  -ms-filter: \"progid:DXImageTransform.Microsoft.BasicImage(rotation=1)\";\n  -webkit-transform: rotate(90deg);\n          transform: rotate(90deg); }\n\n.fa-rotate-180 {\n  -ms-filter: \"progid:DXImageTransform.Microsoft.BasicImage(rotation=2)\";\n  -webkit-transform: rotate(180deg);\n          transform: rotate(180deg); }\n\n.fa-rotate-270 {\n  -ms-filter: \"progid:DXImageTransform.Microsoft.BasicImage(rotation=3)\";\n  -webkit-transform: rotate(270deg);\n          transform: rotate(270deg); }\n\n.fa-flip-horizontal {\n  -ms-filter: \"progid:DXImageTransform.Microsoft.BasicImage(rotation=0, mirror=1)\";\n  -webkit-transform: scale(-1, 1);\n          transform: scale(-1, 1); }\n\n.fa-flip-vertical {\n  -ms-filter: \"progid:DXImageTransform.Microsoft.BasicImage(rotation=2, mirror=1)\";\n  -webkit-transform: scale(1, -1);\n          transform: scale(1, -1); }\n\n.fa-flip-horizontal.fa-flip-vertical {\n  -ms-filter: \"progid:DXImageTransform.Microsoft.BasicImage(rotation=2, mirror=1)\";\n  -webkit-transform: scale(-1, -1);\n          transform: scale(-1, -1); }\n\n:root .fa-rotate-90,\n:root .fa-rotate-180,\n:root .fa-rotate-270,\n:root .fa-flip-horizontal,\n:root .fa-flip-vertical {\n  -webkit-filter: none;\n          filter: none; }\n\n.fa-stack {\n  display: inline-block;\n  height: 2em;\n  position: relative;\n  width: 2em; }\n\n.fa-stack-1x,\n.fa-stack-2x {\n  bottom: 0;\n  left: 0;\n  margin: auto;\n  position: absolute;\n  right: 0;\n  top: 0; }\n\n.svg-inline--fa.fa-stack-1x {\n  height: 1em;\n  width: 1em; }\n\n.svg-inline--fa.fa-stack-2x {\n  height: 2em;\n  width: 2em; }\n\n.fa-inverse {\n  color: #fff; }\n\n.sr-only {\n  border: 0;\n  clip: rect(0, 0, 0, 0);\n  height: 1px;\n  margin: -1px;\n  overflow: hidden;\n  padding: 0;\n  position: absolute;\n  width: 1px; }\n\n.sr-only-focusable:active, .sr-only-focusable:focus {\n  clip: auto;\n  height: auto;\n  margin: 0;\n  overflow: visible;\n  position: static;\n  width: auto; }\n"],["LINK",{"rel":"stylesheet","type":"text/css","href":"css/sample-app-web.css"}],"\n\n"],"\n",["BODY",{"class":"main-body"},"\n",["DIV",{"id":"page_wrapper","class":"page_wrapper"},"\n    ",["DIV",{"id":"menu_button_container"},["DIV",{},["DIV",{"class":"bm-overlay","style":"position: fixed; z-index: 1000; width: 100%; height: 100%; background: rgba(0, 0, 0, 0.3); opacity: 0; transform: translate3d(100%, 0px, 0px); transition: opacity 0.3s, transform 0.3s;"}],["DIV",{"id":"","class":"bm-menu-wrap","style":"position: fixed; right: inherit; z-index: 1100; width: 300px; height: 100%; transform: translate3d(-100%, 0px, 0px); transition: 0.5s;"},["DIV",{"class":"bm-menu","style":"height: 100%; box-sizing: border-box; overflow: auto;"},["NAV",{"class":"bm-item-list","style":"height: 100%;"},["A",{"id":"inventory_sidebar_link","class":"bm-item menu-item","href":"./inventory.html","style":"display: block; outline: none;"},"All Items"],["A",{"id":"about_sidebar_link","class":"bm-item menu-item","href":"https://saucelabs.com/","style":"display: block; outline: none;"},"About"],["A",{"id":"logout_sidebar_link","class":"bm-item menu-item","href":"./index.html","style":"display: block; outline: none;"},"Logout"],["A",{"id":"reset_sidebar_link","class":"bm-item menu-item","style":"display: block; outline: none;"},"Reset App State"]]],["DIV",{},["DIV",{"class":"bm-cross-button","style":"position: absolute; width: 24px; height: 24px; right: 8px; top: 8px;"},["SPAN",{"style":"position: absolute; top: 6px; right: 14px;"},["SPAN",{"class":"bm-cross","style":"position: absolute; width: 3px; height: 14px; transform: rotate(45deg);"}],["SPAN",{"class":"bm-cross","style":"position: absolute; width: 3px; height: 14px; transform: rotate(-45deg);"}]],["BUTTON",{"style":"position: absolute; left: 0px; top: 0px; width: 100%; height: 100%; margin: 0px; padding: 0px; border: none; font-size: 0px; background: transparent; color: transparent; outline: none; cursor: pointer;"},"Close Menu"]]]],["DIV",{},["DIV",{"class":"bm-burger-button","style":"z-index: 1000;"},["SPAN",{},["SPAN",{"class":"bm-burger-bars","style":"position: absolute; height: 20%; left: 0px; right: 0px; top: 0%; opacity: 1;"}],["SPAN",{"class":"bm-burger-bars","style":"position: absolute; height: 20%; left: 0px; right: 0px; top: 40%; opacity: 1;"}],["SPAN",{"class":"bm-burger-bars","style":"position: absolute; height: 20%; left: 0px; right: 0px; top: 80%; opacity: 1;"}]],["BUTTON",{"style":"position: absolute; left: 0px; top: 0px; width: 100%; height: 100%; margin: 0px; padding: 0px; border: none; opacity: 0; font-size: 8px; cursor: pointer;"},"Open Menu"]]]]],"\n    ",["DIV",{"id":"contents_wrapper"},"\n        ",["DIV",{"id":"header_container","class":"header_container"},"\n            ",["DIV",{"class":"header_label"},"\n                ",["DIV",{"class":"app_logo"}],"\n            "],"\n            ",["DIV",{"id":"shopping_cart_container","class":"shopping_cart_container"},["A",{"href":"./cart.html","class":"shopping_cart_link fa-layers fa-fw"},["svg",{"aria-hidden":"true","data-prefix":"fas","data-icon":"shopping-cart","class":"svg-inline--fa fa-shopping-cart fa-w-18 fa-3x ","role":"img","xmlns":"http://www.w3.org/2000/svg","viewBox":"0 0 576 512"},["path",{"fill":"currentColor","d":"M528.12 301.319l47.273-208C578.806 78.301 567.391 64 551.99 64H159.208l-9.166-44.81C147.758 8.021 137.93 0 126.529 0H24C10.745 0 0 10.745 0 24v16c0 13.255 10.745 24 24 24h69.883l70.248 343.435C147.325 417.1 136 435.222 136 456c0 30.928 25.072 56 56 56s56-25.072 56-56c0-15.674-6.447-29.835-16.824-40h209.647C430.447 426.165 424 440.326 424 456c0 30.928 25.072 56 56 56s56-25.072 56-56c0-22.172-12.888-41.332-31.579-50.405l5.517-24.276c3.413-15.018-8.002-29.319-23.403-29.319H218.117l-6.545-32h293.145c11.206 0 20.92-7.754 23.403-18.681z"}]],""]],"\n        "],"\n        ",["DIV",{"id":"inventory_container"},["DIV",{},["DIV",{"class":"header_secondary_container"},["DIV",{"class":"peek"}],["DIV",{"id":"searchbox_container"}],["DIV",{"id":"inventory_filter_container"},["DIV",{"class":"product_label"},"Products"],["SELECT",{"class":"product_sort_container"},["OPTION",{"__playwright_selected_":"true","value":"az"},"Name (A to Z)"],["OPTION",{"__playwright_selected_":"false","value":"za"},"Name (Z to A)"],["OPTION",{"__playwright_selected_":"false","value":"lohi"},"Price (low to high)"],["OPTION",{"__playwright_selected_":"false","value":"hilo"},"Price (high to low)"]]]],["DIV",{"id":"inventory_container","class":"inventory_container"},["DIV",{"class":"inventory_list"},["DIV",{"class":"inventory_item"},["DIV",{"class":"inventory_item_img"},["A",{"href":"./inventory-item.html?id=4","id":"item_4_img_link"},["IMG",{"__playwright_current_src__":"","class":"inventory_item_img","src":"./img/sauce-backpack-1200x1500.jpg"}]]],["DIV",{"class":"inventory_item_label"},["A",{"href":"./inventory-item.html?id=4","id":"item_4_title_link"},["DIV",{"class":"inventory_item_name"},"Sauce Labs Backpack"]],["DIV",{"class":"inventory_item_desc"},"carry.allTheThings() with the sleek, streamlined Sly Pack that melds uncompromising style with unequaled laptop and tablet protection."]],["DIV",{"class":"pricebar"},["DIV",{"class":"inventory_item_price"},"$","29.99"],["BUTTON",{"class":"btn_primary btn_inventory"},"ADD TO CART"]]],["DIV",{"class":"inventory_item"},["DIV",{"class":"inventory_item_img"},["A",{"href":"./inventory-item.html?id=0","id":"item_0_img_link"},["IMG",{"__playwright_current_src__":"","class":"inventory_item_img","src":"./img/bike-light-1200x1500.jpg"}]]],["DIV",{"class":"inventory_item_label"},["A",{"href":"./inventory-item.html?id=0","id":"item_0_title_link"},["DIV",{"class":"inventory_item_name"},"Sauce Labs Bike Light"]],["DIV",{"class":"inventory_item_desc"},"A red light isn't the desired state in testing but it sure helps when riding your bike at night. Water-resistant with 3 lighting modes, 1 AAA battery included."]],["DIV",{"class":"pricebar"},["DIV",{"class":"inventory_item_price"},"$","9.99"],["BUTTON",{"class":"btn_primary btn_inventory"},"ADD TO CART"]]],["DIV",{"class":"inventory_item"},["DIV",{"class":"inventory_item_img"},["A",{"href":"./inventory-item.html?id=1","id":"item_1_img_link"},["IMG",{"__playwright_current_src__":"","class":"inventory_item_img","src":"./img/bolt-shirt-1200x1500.jpg"}]]],["DIV",{"class":"inventory_item_label"},["A",{"href":"./inventory-item.html?id=1","id":"item_1_title_link"},["DIV",{"class":"inventory_item_name"},"Sauce Labs Bolt T-Shirt"]],["DIV",{"class":"inventory_item_desc"},"Get your testing superhero on with the Sauce Labs bolt T-shirt. From American Apparel, 100% ringspun combed cotton, heather gray with red bolt."]],["DIV",{"class":"pricebar"},["DIV",{"class":"inventory_item_price"},"$","15.99"],["BUTTON",{"class":"btn_primary btn_inventory"},"ADD TO CART"]]],["DIV",{"class":"inventory_item"},["DIV",{"class":"inventory_item_img"},["A",{"href":"./inventory-item.html?id=5","id":"item_5_img_link"},["IMG",{"__playwright_current_src__":"","class":"inventory_item_img","src":"./img/sauce-pullover-1200x1500.jpg"}]]],["DIV",{"class":"inventory_item_label"},["A",{"href":"./inventory-item.html?id=5","id":"item_5_title_link"},["DIV",{"class":"inventory_item_name"},"Sauce Labs Fleece Jacket"]],["DIV",{"class":"inventory_item_desc"},"It's not every day that you come across a midweight quarter-zip fleece jacket capable of handling everything from a relaxing day outdoors to a busy day at the office."]],["DIV",{"class":"pricebar"},["DIV",{"class":"inventory_item_price"},"$","49.99"],["BUTTON",{"class":"btn_primary btn_inventory"},"ADD TO CART"]]],["DIV",{"class":"inventory_item"},["DIV",{"class":"inventory_item_img"},["A",{"href":"./inventory-item.html?id=2","id":"item_2_img_link"},["IMG",{"__playwright_current_src__":"","class":"inventory_item_img","src":"./img/red-onesie-1200x1500.jpg"}]]],["DIV",{"class":"inventory_item_label"},["A",{"href":"./inventory-item.html?id=2","id":"item_2_title_link"},["DIV",{"class":"inventory_item_name"},"Sauce Labs Onesie"]],["DIV",{"class":"inventory_item_desc"},"Rib snap infant onesie for the junior automation engineer in development. Reinforced 3-snap bottom closure, two-needle hemmed sleeved and bottom won't unravel."]],["DIV",{"class":"pricebar"},["DIV",{"class":"inventory_item_price"},"$","7.99"],["BUTTON",{"class":"btn_primary btn_inventory"},"ADD TO CART"]]],["DIV",{"class":"inventory_item"},["DIV",{"class":"inventory_item_img"},["A",{"href":"./inventory-item.html?id=3","id":"item_3_img_link"},["IMG",{"__playwright_current_src__":"","class":"inventory_item_img","src":"./img/red-tatt-1200x1500.jpg"}]]],["DIV",{"class":"inventory_item_label"},["A",{"href":"./inventory-item.html?id=3","id":"item_3_title_link"},["DIV",{"class":"inventory_item_name"},"Test.allTheThings() T-Shirt (Red)"]],["DIV",{"class":"inventory_item_desc"},"This classic Sauce Labs t-shirt is perfect to wear when cozying up to your keyboard to automate a few tests. Super-soft and comfy ringspun combed cotton."]],["DIV",{"class":"pricebar"},["DIV",{"class":"inventory_item_price"},"$","15.99"],["BUTTON",{"class":"btn_primary btn_inventory"},"ADD TO CART"]]]]]]],"\n    "],"\n    ",["DIV",{"class":"push"}],"\n"],"\n",["FOOTER",{"class":"footer"},"\n\n    ",["UL",{"class":"social"},"\n        ",["LI",{"class":"social_twitter"},"Twitter"],"\n        ",["LI",{"class":"social_facebook"},"Facebook"],"\n        ",["LI",{"class":"social_linkedin"},"LinkedIn"],"\n    "],"\n\n    ",["DIV",{"class":"footer_copy"},"© 2020 Sauce Labs. All Rights Reserved. Terms of Service | Privacy Policy"],"\n    ",["IMG",{"__playwright_current_src__":"","class":"footer_robot","src":"img/SwagBot_Footer_graphic.png"}],"\n"],"\n","\n\n",["DIV",{"id":"mcp-sidebar","style":"position: fixed; top: 0px; right: 0px; width: 500px; height: 100vh; background: rgb(245, 245, 245); border-left: 1px solid rgb(228, 228, 231); z-index: 999999; display: flex; flex-direction: column; overflow: hidden; transition: transform 0.3s;"},["DIV",{"id":"mcp-resize-handle","style":"position: absolute; left: 0px; top: 0px; width: 4px; height: 100%; cursor: ew-resize; background: transparent;"}],["IFRAME",{"__playwright_bounding_rect__":"{\"left\":700,\"top\":0,\"right\":1200,\"bottom\":894}","src":"","name":"toolbox-frame","style":"width: 100%; height: 100%; border: none;"}]],["BUTTON",{"id":"mcp-sidebar-toggle-button","data-skip-recording":"true","style":"position: fixed; right: 500px; top: 50%; transform: translateY(-50%); background: rgb(245, 245, 245); border-top: 1px solid rgb(228, 228, 231); border-bottom: 1px solid rgb(228, 228, 231); border-left: 1px solid rgb(228, 228, 231); border-image: initial; border-right: none; border-radius: 4px 0px 0px 4px; font-size: 20px; cursor: pointer; padding: 8px; color: rgb(17, 24, 39); z-index: 999999; transition: right 0.3s;"},"⟩"]]],"viewport":{"width":1200,"height":894},"timestamp":198425.955,"wallTime":1755287565289,"collectionTime":2.200000047683716,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@69","startTime":198427.702,"title":"Wait for load state \"load\"","class":"Page","method":"waitForEventInfo","params":{"info":{"waitId":"63d62ae568c660b2abd5c4a3077fda3e","phase":"before","event":""}},"pageId":"page@66ecceb74b017ecb20d1b87d48fb4ceb","beforeSnapshot":"before@call@69"}
{"type":"screencast-frame","pageId":"page@66ecceb74b017ecb20d1b87d48fb4ceb","sha1":"<EMAIL>","width":1200,"height":894,"timestamp":198428.175,"frameSwapWallTime":1755287565350.068}
{"type":"screencast-frame","pageId":"page@66ecceb74b017ecb20d1b87d48fb4ceb","sha1":"<EMAIL>","width":1200,"height":894,"timestamp":198428.265,"frameSwapWallTime":1755287565350.7668}
{"type":"frame-snapshot","snapshot":{"callId":"call@69","snapshotName":"before@call@69","pageId":"page@66ecceb74b017ecb20d1b87d48fb4ceb","frameId":"frame@9b484cf0b4c527045bd2101a2ffc781f","frameUrl":"https://www.saucedemo.com/v1/inventory.html","doctype":"html","html":[[1,211]],"viewport":{"width":1200,"height":894},"timestamp":198430.005,"wallTime":1755287565354,"collectionTime":0.2999999523162842,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@71","startTime":198441.318,"class":"Frame","method":"evaluateExpression","params":{"expression":"(state2) => {\n      window.globalState = state2;\n      window.notifyStateSubscribers();\n    }","isFunction":true,"arg":{"value":{"o":[{"k":"messages","v":{"a":[],"id":2}},{"k":"pickingType","v":{"v":"null"}},{"k":"recordingInteractions","v":{"b":false}},{"k":"code","v":{"s":"async function run(page) {\n    let title = await page.title();\n    return title\n}"}}],"id":1},"handles":[]}},"pageId":"page@66ecceb74b017ecb20d1b87d48fb4ceb","beforeSnapshot":"before@call@71"}
{"type":"frame-snapshot","snapshot":{"callId":"call@71","snapshotName":"before@call@71","pageId":"page@66ecceb74b017ecb20d1b87d48fb4ceb","frameId":"frame@9b484cf0b4c527045bd2101a2ffc781f","frameUrl":"https://www.saucedemo.com/v1/inventory.html","doctype":"html","html":["HTML",{},[[2,13]],[[2,14]],["BODY",{"class":"main-body"},[[2,15]],[[2,182]],[[2,183]],[[2,202]],[[2,203]],[[2,204]],["DIV",{"id":"mcp-sidebar","style":"position: fixed; top: 0px; right: 0px; width: 500px; height: 100vh; background: rgb(245, 245, 245); border-left: 1px solid rgb(228, 228, 231); z-index: 999999; display: flex; flex-direction: column; overflow: hidden; transition: transform 0.3s;"},[[2,205]],["IFRAME",{"__playwright_bounding_rect__":"{\"left\":700,\"top\":0,\"right\":1200,\"bottom\":894}","src":"/snapshot/frame@d4e14c4ae7c2cb07a69a2fc17ab68003","name":"toolbox-frame","style":"width: 100%; height: 100%; border: none;"}]],[[2,209]]]],"viewport":{"width":1200,"height":894},"timestamp":198442.216,"wallTime":1755287565368,"collectionTime":0.40000009536743164,"resourceOverrides":[],"isMainFrame":true}}
{"type":"frame-snapshot","snapshot":{"callId":"call@71","snapshotName":"before@call@71","pageId":"page@66ecceb74b017ecb20d1b87d48fb4ceb","frameId":"frame@d4e14c4ae7c2cb07a69a2fc17ab68003","frameUrl":"http://localhost:5174/","doctype":"html","html":["HTML",{"lang":"en"},["HEAD",{},["BASE",{"href":"http://localhost:5174/"}],"\n    ",["META",{"charset":"utf-8"}],"\n    ",["LINK",{"rel":"icon","type":"image/svg+xml","href":"/vite.svg"}],"\n    ",["META",{"name":"viewport","content":"width=device-width, initial-scale=1.0"}],"\n    ",["TITLE",{},"Vite + React + TS"],"\n    ","\n    ",["LINK",{"rel":"stylesheet","crossorigin":"","href":"/assets/index-Dqrek7rF.css"}],"\n  "],"\n  ",["BODY",{},"\n    ",["DIV",{"id":"root"}],"\n    ","\n  \n\n"]],"viewport":{"width":500,"height":894},"timestamp":198443.718,"wallTime":1755287565369,"collectionTime":1.3999998569488525,"resourceOverrides":[],"isMainFrame":false}}
{"type":"after","callId":"call@71","endTime":198444.529,"result":{"value":{"v":"undefined"}},"afterSnapshot":"after@call@71"}
{"type":"frame-snapshot","snapshot":{"callId":"call@71","snapshotName":"after@call@71","pageId":"page@66ecceb74b017ecb20d1b87d48fb4ceb","frameId":"frame@d4e14c4ae7c2cb07a69a2fc17ab68003","frameUrl":"http://localhost:5174/","doctype":"html","html":[[1,21]],"viewport":{"width":500,"height":894},"timestamp":198445.122,"wallTime":1755287565371,"collectionTime":0.09999990463256836,"resourceOverrides":[],"isMainFrame":false}}
{"type":"frame-snapshot","snapshot":{"callId":"call@71","snapshotName":"after@call@71","pageId":"page@66ecceb74b017ecb20d1b87d48fb4ceb","frameId":"frame@9b484cf0b4c527045bd2101a2ffc781f","frameUrl":"https://www.saucedemo.com/v1/inventory.html","doctype":"html","html":[[1,3]],"viewport":{"width":1200,"height":894},"timestamp":198445.186,"wallTime":1755287565371,"collectionTime":0.3000001907348633,"resourceOverrides":[],"isMainFrame":true}}
{"type":"screencast-frame","pageId":"page@66ecceb74b017ecb20d1b87d48fb4ceb","sha1":"<EMAIL>","width":1200,"height":894,"timestamp":198470.456,"frameSwapWallTime":1755287565393.765}
{"type":"screencast-frame","pageId":"page@66ecceb74b017ecb20d1b87d48fb4ceb","sha1":"<EMAIL>","width":1200,"height":894,"timestamp":198486.571,"frameSwapWallTime":1755287565409.647}
{"type":"screencast-frame","pageId":"page@66ecceb74b017ecb20d1b87d48fb4ceb","sha1":"<EMAIL>","width":1200,"height":894,"timestamp":198685.242,"frameSwapWallTime":1755287565605.918}
{"type":"screencast-frame","pageId":"page@66ecceb74b017ecb20d1b87d48fb4ceb","sha1":"<EMAIL>","width":1200,"height":894,"timestamp":200150.014,"frameSwapWallTime":1755287567071.402}
{"type":"screencast-frame","pageId":"page@66ecceb74b017ecb20d1b87d48fb4ceb","sha1":"<EMAIL>","width":1200,"height":894,"timestamp":200200.786,"frameSwapWallTime":1755287567124.069}
{"type":"screencast-frame","pageId":"page@66ecceb74b017ecb20d1b87d48fb4ceb","sha1":"<EMAIL>","width":1200,"height":894,"timestamp":200232.072,"frameSwapWallTime":1755287567155.387}
{"type":"screencast-frame","pageId":"page@66ecceb74b017ecb20d1b87d48fb4ceb","sha1":"<EMAIL>","width":1200,"height":894,"timestamp":200269.769,"frameSwapWallTime":1755287567193.1}
{"type":"screencast-frame","pageId":"page@66ecceb74b017ecb20d1b87d48fb4ceb","sha1":"<EMAIL>","width":1200,"height":894,"timestamp":200286.958,"frameSwapWallTime":1755287567207.597}
{"type":"screencast-frame","pageId":"page@66ecceb74b017ecb20d1b87d48fb4ceb","sha1":"<EMAIL>","width":1200,"height":894,"timestamp":200301.974,"frameSwapWallTime":1755287567222.485}
{"type":"screencast-frame","pageId":"page@66ecceb74b017ecb20d1b87d48fb4ceb","sha1":"<EMAIL>","width":1200,"height":894,"timestamp":200323.029,"frameSwapWallTime":1755287567245.914}
{"type":"screencast-frame","pageId":"page@66ecceb74b017ecb20d1b87d48fb4ceb","sha1":"<EMAIL>","width":1200,"height":894,"timestamp":200383.347,"frameSwapWallTime":1755287567305.018}
{"type":"screencast-frame","pageId":"page@66ecceb74b017ecb20d1b87d48fb4ceb","sha1":"<EMAIL>","width":1200,"height":894,"timestamp":200478.395,"frameSwapWallTime":1755287567400.743}
{"type":"screencast-frame","pageId":"page@66ecceb74b017ecb20d1b87d48fb4ceb","sha1":"<EMAIL>","width":1200,"height":894,"timestamp":200656.059,"frameSwapWallTime":1755287567575.769}
{"type":"screencast-frame","pageId":"page@66ecceb74b017ecb20d1b87d48fb4ceb","sha1":"<EMAIL>","width":1200,"height":894,"timestamp":200668.605,"frameSwapWallTime":1755287567588.519}
{"type":"log","callId":"call@69","time":200746.758,"message":"  \"load\" event fired"}
{"type":"after","callId":"call@69","endTime":200746.779,"afterSnapshot":"after@call@69"}
