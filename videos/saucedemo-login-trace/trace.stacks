{"files": ["eval.js", "/Users/<USER>/Documents/TestVista/ACM_MM_2025/opencode_testvista-dev/node_modules/playwright-mcp/dist/server.js", "async file:///Users/<USER>/Documents/TestVista/ACM_MM_2025/opencode_testvista-dev/node_modules/playwright-mcp/dist/server.js", "async file:///Users/<USER>/Documents/TestVista/ACM_MM_2025/opencode_testvista-dev/node_modules/@modelcontextprotocol/sdk/dist/esm/server/mcp.js"], "stacks": [[62, [[0, 8, 14, "run"], [1, 75, 27, "async secureEvalAsync"], [2, 971, 20, ""], [3, 85, 30, ""]]], [64, [[0, 10, 14, "run"], [1, 75, 27, "async secureEvalAsync"], [2, 971, 20, ""], [3, 85, 30, ""]]], [66, [[0, 12, 14, "run"], [1, 75, 27, "async secureEvalAsync"], [2, 971, 20, ""], [3, 85, 30, ""]]], [69, [[0, 14, 14, "run"], [1, 75, 27, "async secureEvalAsync"], [2, 971, 20, ""], [3, 85, 30, ""]]], [71, [[1, 128, 24, "syncToReact"]]]]}