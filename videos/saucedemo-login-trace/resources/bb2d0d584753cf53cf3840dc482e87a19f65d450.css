body, html {
	font-family: '<PERSON><PERSON>', Arial, Helvetica, sans-serif;
	font-size: 14px;
	color: #4A4A4A;
	height: 100%;
	margin: 0;
}
.login_logo {
	text-align: center;
	padding: 30px 0;
	background: url('../img/SwagLabs_logo.png') no-repeat center center;
	margin-top: 30px;
}
.app_logo {
	background: url('../img/SwagLabs_logo.png') no-repeat center center;
	text-align: center;
	margin: 0 50px;
	height: 35px;
}
.peek {
	background: url('../img/peek.png') no-repeat;
	width: 100px;
	height: 100px;
	position: absolute;
	left: 15px;
	bottom: -40px;
	z-index: 2;
}

input {
	-webkit-appearance: none;
	-moz-appearance: none;
	appearance: none;
	border-radius: 0;
}

/* LOGIN SCREEN */
.login_wrapper{
	padding: 30px 0;
	margin: 0px auto;
}
.login_wrapper-inner {
	overflow: hidden;
}
.login_wrapper-inner,
.login_credentials_wrap-inner {
	width: 70%;
	max-width: 720px;
	padding: 15px 30px 30px;
	margin: 0 auto;
	border: 2px solid #ededed;
}
.form_column {
	float: none;
	width: 100%;
}
.bot_column {
	float: none;
	width: 100%;
	height: auto;
}
@media only screen and (min-width: 900px) {
	.form_column {
		float: left;
		width: 48%;
		padding-top: 30px;
	}
	.bot_column {
		float: right;
		width: 48%;
		background: url('../img/Login_Bot_graphic.png') no-repeat center center;
		min-height: 260px;
		min-width: 300px;
	}
}
.login-button {
	margin: 20px auto;
	background: #fff;
	border: 2px solid #CE2A2B;
	font-size: 18px;
	width: 100%;
	max-width: 400px;
	padding: 10px;
	text-align: center;
	color: #CE2A2B;
	outline: none;
}
.login-button:hover {
	background: #efefef;
	cursor: pointer;
}
.login-button:active {
	background: #ccc;
}
.form_input {
	margin: 0 0 25px 0;
	font-size: 18px;
	width: 100%;
	max-width: 380px;
	border: 0px;
	border-bottom: 1px solid #ccc;
	outline: none;
	padding: 10px 0;
}
.login_credentials {
	clear: both;
	line-height: 24px;
}
.login_credentials {
	float: left;
	width: 48%;
}
.login_password{
	float: right;
	width: 48%;
}
.login_credentials_wrap-inner{
	background-color: #efefef;
	overflow: hidden;
}
.error-button {
	border: 0px;
	background: #fff;
	color: #f00;
	cursor: pointer;
}
/* END LOGIN SCREEN */

.main-body {
	margin: 0px;
}
.header_label {
	background: #fff;
	padding: 18px 18px 0;
	text-align: center;
	font-size: 1.7em;
	position: relative;
}
.header_container{
	height: 70px;
}

.header_secondary_container {
	background: #474c55;
	width: 100%;
	height: 80px;
	position: relative; /* Need this here so the position: absolute on subelements anchors here rather than to the browser viewport */
}

.subheader {
	position: relative; /* Need this here so the position: absolute on subelements anchors here rather than to the browser viewport */
	background: #474c55;
	height: 70px;
	color: #fff;
	font-size: 2em;
	padding-left: 15px;
	line-height: 66px;
}

.product_label,
.subheader_label {
	position: absolute;
	left: 110px;
	top: 25px;
	font-size: 2.2em;
	font-weight: 100;
	color: #fff;
}

.inventory_item_container{
	border-top: 1px solid #777;
}

.inventory_list,
.cart_contents_container,
.inventory_details_container,
.checkout_summary_container,
.checkout_complete_container {
	max-width: 1280px;
	margin: 0 auto;
	padding: 0 15px 30px;
}
.inventory_item_name {
	font-size: 1.4em;
	font-weight: 500;
	color: #E2231A;
}
.inventory_item_desc {
	font-size: .85em;
	margin-top: 7px;
	color: #777777;
}
.pricebar {
	position: absolute;
	bottom: 10px;
	right: 10px;
	width: 74%;
}
.checkout_button{
	position: absolute;
	right: 0;
	top: 20px;
}

.inventory_item_price {
	color: #E2231A;
	font-size: 1.6em;
	display: inline-block;
}
.btn_inventory {
	position: absolute;
	right: 0;
	bottom: 0;
}
.inventory_container {
	overflow: hidden;
	margin-bottom: 30px;
}
.inventory_item {
	border: 2px solid #EDEDEF;
	background: #fff;
	width: 100%;
	position: relative; /* Need this here so the position: absolute on subelements anchors here rather than to the browser viewport */
	overflow: hidden;
	margin-bottom: 12px;
}
.inventory_item_label {
	padding-top: 15px;
	min-height: 180px;
	width: 74%;
	float: right;
	vertical-align: top;
	position: relative;
}
.inventory_item:nth-child(odd) {
	margin-right: 20px;
}

@media only screen and (min-width: 900px) {
	.inventory_item_label {
		min-height: 150px;
	}
}
/* Adjust inventory item width for larger screens */
@media only screen and (min-width: 1200px) {
	.inventory_item {
		width: 45%;
		float: left;
	}
	.inventory_item_label {
		width: 72%;
	}
	.inventory_container {
		padding-top: 40px;
	}
	.pricebar {
		width: 71%;
	}

}
.inventory_item_label > a,
.cart_item_label > a {
	color: #4A4A4A;
	text-decoration: none;
}
.inventory_item_img {
	float: left;
	width: 24%;
}
.inventory_details_container {
	margin-top: 50px;
}
.inventory_item_img img{
	width: 98%;
}
.inventory_details {
	background: #fff;
	width: 100%;
	position: relative; /* Need this here so the position: absolute on subelements anchors here rather than to the browser viewport */
}
.inventory_details_name {
	font-size: 1.2em;
	margin-bottom: 10px;
	font-weight: 500;
}
.inventory_details_desc_container {
	padding: 20px 20px 0;
	vertical-align: top;
	display: inline-block;
	width: 50%;
	position: relative; /* Need this here so the position: absolute on subelements anchors here rather than to the browser viewport */
}
.inventory_details_desc {
	font-size: 1em;
	margin-bottom: 20px;
}
.inventory_details_back_button {
	margin: 10px auto;
	background: #57c1e8;
	font-size: 18px;
	font-weight: 500;
	padding: 10px;
	text-align: center;
	color: #fff;
	position: absolute;
	right: 10px;
	padding: 10px 20px;
	border: none;
	right: 15px;
}
.inventory_details_price {
	color: #569210;
	font-size: 48px;
}

/*========= SHOPPING CART ==========*/
.shopping_cart_container {
	height: 32px;
	position: absolute;
	top: 30px;
	right: 40px;
}

.shopping_cart_link {
	color: #777;
}

.shopping_cart_link > svg {
	width: 30px !important;
}

.shopping_cart_badge {
	font-weight: 400;
	top: -20px;
	right: -30px;
	transform: scale(1.1);
}

.cart_item_label {
	padding: 20px 0 0 10px;
	width: calc(99% - 165px);
	display: inline-block;
	vertical-align: top;
	position: relative;
}

.cart_item {
	border-top: 2px solid #EDEDEF;
	background: #fff;
	width: 100%;
	position: relative;
}

.item_pricebar {
	margin-top: 20px;
}

.cart_quantity {
	height: 20px;
	display: inline-block;
	text-align: center;
	font-size: 24px;
	margin: 60px 50px;
	border: 1px solid #ededed;
	padding: 20px;
}

.cart_contents_container {
	padding-top: 30px;
}

.cart_quantity_label {
	display: inline-block;
	font-size: 18px;
	padding: 20px 60px;
	background-color: #fff;
	color: #484C55;
}
.cart_desc_label {
	font-size: 18px;
	color: #484C55;
	display: inline-block;
}
.menu-item {
	cursor: pointer;
}
.cart_footer {
	position: relative; /* Need this here so the position: absolute on subelements anchors here rather than to the browser viewport */
	border-top: 2px solid #EDEDEF;
	padding: 20px 0;
	margin-bottom: 30px;
}

.checkout_info_wrapper{
	padding: 0 15px;
}

.checkout_info_container {
	max-width: 600px;
	margin: 0 auto;
	padding-top: 30px;
}
.checkout_info {
	max-width: 380px;
	margin: 0 auto;
	padding-top: 30px;
}
.checkout_buttons{
	margin-top: 50px;
	border-top: 2px solid #EDEDEF;
	padding: 20px 0;
}
.cart_button {
	float: right;
}

.summary_quantity {
	height: 20px;
	display: inline-block;
	text-align: center;
	font-size: 24px;
	margin: 60px 50px;
	padding: 5px 30px 5px 10px;
}

.summary_info_label {
	width: 100%;
	border-top: 1px solid #efefef;
	padding: 20px 0 0;
	font-size: 1.2em;
}

.summary_value_label {
	font-weight: 800;
	font-size: 18px;
	padding: 20px 0;
}

.summary_subtotal_label {
	border-top: 1px solid #efefef;
	padding: 20px 0 10px;
	font-size: 18px;
}

.summary_tax_label {
	padding: 0;
	font-size: 1.2em;
}

.summary_total_label {
	padding: 10px 0;
	font-size: 1.2em;
	margin: 0px 0px 8px 0px;
}

.checkout_complete_container {
	text-align: center;
}
.pony_express {
	width: 100%;
	height: 100%;
	max-width: 523px;
	max-height: 381px;
	margin: 30px auto;
}

/*============ ITEM DETAILS ===============*/
.inventory_details_img {
	width: 100%;
	max-width: 400px;
}

.product_sort_container {
	position: absolute;
	right: 20px;
	top: 23px;
	height: 35px;
	width: 115px;
}

@media only screen and (max-width: 960px) {
	.inventory_details_desc_container{
		width: 100%;
		padding-left: 0;
		padding-right: 0;
	}
}

/* ===============Burger button styling==================== */
/* Position and sizing of burger button */
.bm-burger-button {
	position: absolute;
	width: 20px;
	height: 15px;
	left: 15px;
	top: 30px;
}

/* Color/shape of burger icon bars */
.bm-burger-bars {
	background: #777;
}

/* Position and sizing of clickable cross button */
.bm-cross-button {
	height: 24px;
	width: 24px;
}

/* Color/shape of close button cross */
.bm-cross {
	background: #bdc3c7;
}

/* General sidebar styles */
.bm-menu {
	background: #fff;
	padding: 2.5em 1.5em 0;
	font-size: 1.15em;
	box-shadow: 5px 0px 5px 0 rgba(0,0,0,0.16);
}

/* Morph shape necessary with bubble or elastic */
.bm-morph-shape {
	fill: #373a47;
}

/* Wrapper for item list */
.bm-item-list {
	color: #b8b7ad;
}

/* Individual item */
.bm-item {
	display: inline-block;
	color: #666;
	font-weight: 300;
	margin-bottom: 7px;
	text-decoration: none;
	padding: 4px;
}

/* Styling of overlay */
.bm-overlay {
	background: rgba(0, 0, 0, 0.3);
}

/* Sticky Footer */
.footer {
	background-color: #474C55;
	height: 360px;
	position: relative;
}
.page_wrapper {
	margin-bottom: -360px;
	min-height: 100%;
}
.push {
	height: 360px;
}
@media only screen and (min-width: 1200px) {
	.page_wrapper {
		margin-bottom: -360px;
	}
}
.footer_copy,
.footer_robot,
.social {
	position: absolute;
}
.footer_copy {
	left: 50px;
	bottom: 40px;
	color: #fff;
}
.social {
	list-style-type: none;
	left: 10px;
	top: 20px;
}
.social li {
	float: left;
	margin-right: 20px;
	display: inline-block;
	width: 32px;
	height: 32px;
	text-indent: -999em;
}
.social_twitter {
	background: url('../img/twitter.png') no-repeat;
}
.social_facebook {
	background: url('../img/facebook.png') no-repeat;
}
.social_linkedin {
	background: url('../img/linkedIn.png') no-repeat;
}

.footer_robot {
	width: 470px;
	height: 352px;
	right: 0;
	bottom: 0;
}

@media only screen and (max-width: 960px) {
	.footer{
		height: auto;
		text-align: center;
		padding: 0 10px;
	}
	.social, .footer_copy, .footer_robot{
		position: relative;
	}
	.social{
		width: 100%;
		padding: 0;
		left: 0;
	}
	.social li{
		float: none;
	}

	.footer_copy{
		width: 100%;
		left: 0;
		bottom: 0;
		margin: 40px 0;
	}
	.footer_robot{
		position: relative;
	}
	.footer_robot {
		width: 100%;
		height: 100%;
		max-width: 470px;
		max-height: 352px;
	}
}

/*Button Styles*/
.btn_primary,
.btn_secondary,
.btn_action,
.btn_link {
	cursor: pointer;
	outline: none;
	line-height: 40px;
	padding: 0 20px;
	font-size: 1.1em;
	display: inline-block;
	text-decoration: none;
	text-transform: uppercase;
}
.btn_primary {
	border: 2px solid #E2231A;
	color: #E2231A;
	background-color: #fff;
}
.btn_primary:hover {
	background-color: #efefef;
}
.btn_secondary {
	border: 2px solid #474C55;
	background-color: #fff;
	color: #474C55;
}
.btn_secondary:hover {
	background: #efefef;
}
.btn_action{
	background-color: #E2231A;
	border: 2px solid #E2231A;
	color: #fff;
}
.btn_link {
	background-color: transparent;
	border: 2px solid transparent;
}
