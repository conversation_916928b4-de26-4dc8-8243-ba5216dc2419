## IMPORTANT

- Try to keep things in one function unless composable or reusable
- DO NOT do unnecessary destructuring of variables
- DO NOT use `else` statements unless necessary
- DO NOT use `try`/`catch` if it can be avoided
- AVOID `try`/`catch` where possible
- AVOID `else` statements
- A<PERSON><PERSON> using `any` type
- AVOID `let` statements
- PREFER single word variable names where possible
- Use as many bun apis as possible like Bun.file()


## Testing

### /test must always record video
- When I run the `/test` command, **enable video recording** for the detected test runner.
- Detect Playwright or Cypress automatically. If neither is found, prefer Playwright conventions and explain how to enable recording.
- After the run, print a short summary with the paths to videos and reports.

#### Playwright
- Ensure in `playwright.config.*`:
  - `use.video = 'on'`
  - `use.trace = 'on'`
  - `use.screenshot = 'only-on-failure'`
  - `outputDir = ./artifacts/playwright/output`
- Run with HTML report (or keep existing reporters).
- Place reports under `./artifacts/playwright/report`.

#### Artifacts summary
- After `/test`, show:
  - Playwright: `./artifacts/playwright/report/index.html`, videos in `./artifacts/playwright/output/**`\
- If a conflicting env/flag disables video, **override it** and warn in the summary.
