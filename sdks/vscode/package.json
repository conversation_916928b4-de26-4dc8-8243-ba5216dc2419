{"name": "opencode", "displayName": "opencode", "description": "opencode for VS Code", "version": "0.4.1", "publisher": "sst-dev", "repository": {"type": "git", "url": "https://github.com/sst/opencode"}, "license": "MIT", "icon": "images/icon.png", "galleryBanner": {"color": "#000000", "theme": "dark"}, "engines": {"vscode": "^1.94.0"}, "categories": ["Other"], "activationEvents": [], "main": "./dist/extension.js", "contributes": {"commands": [{"command": "opencode.openTerminal", "title": "Open opencode", "icon": {"light": "images/button-dark.svg", "dark": "images/button-light.svg"}}, {"command": "opencode.openNewTerminal", "title": "Open opencode in new tab", "icon": {"light": "images/button-dark.svg", "dark": "images/button-light.svg"}}, {"command": "opencode.addFilepathToTerminal", "title": "Add Filepath to Terminal"}], "menus": {"editor/title": [{"command": "opencode.openNewTerminal", "group": "navigation"}]}, "keybindings": [{"command": "opencode.openTerminal", "title": "Run opencode", "key": "cmd+escape", "mac": "cmd+escape", "win": "ctrl+escape", "linux": "ctrl+escape"}, {"command": "opencode.openNewTerminal", "title": "Run opencode", "key": "cmd+shift+escape", "mac": "cmd+shift+escape", "win": "ctrl+shift+escape", "linux": "ctrl+shift+escape"}, {"command": "opencode.addFilepathToTerminal", "title": "opencode: Insert At-Mentioned", "key": "cmd+alt+k", "mac": "cmd+alt+k", "win": "ctrl+alt+K", "linux": "ctrl+alt+K"}]}, "scripts": {"vscode:prepublish": "bun run package", "compile": "bun run check-types && bun run lint && node esbuild.js", "watch:esbuild": "node esbuild.js --watch", "watch:tsc": "tsc --noEmit --watch --project tsconfig.json", "package": "bun run check-types && bun run lint && node esbuild.js --production", "compile-tests": "tsc -p . --outDir out", "watch-tests": "tsc -p . -w --outDir out", "pretest": "bun run compile-tests && bun run compile && bun run lint", "check-types": "tsc --noEmit", "lint": "eslint src", "test": "vscode-test"}, "devDependencies": {"@types/vscode": "^1.94.0", "@types/mocha": "^10.0.10", "@types/node": "20.x", "@typescript-eslint/eslint-plugin": "^8.31.1", "@typescript-eslint/parser": "^8.31.1", "eslint": "^9.25.1", "esbuild": "^0.25.3", "typescript": "^5.8.3", "@vscode/test-cli": "^0.0.11", "@vscode/test-electron": "^2.5.2"}}