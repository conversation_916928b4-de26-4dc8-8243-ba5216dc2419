// This file is auto-generated by @hey-api/openapi-ts

export type Event =
  | ({
      type: "installation.updated"
    } & EventInstallationUpdated)
  | ({
      type: "lsp.client.diagnostics"
    } & EventLspClientDiagnostics)
  | ({
      type: "message.updated"
    } & EventMessageUpdated)
  | ({
      type: "message.removed"
    } & EventMessageRemoved)
  | ({
      type: "message.part.updated"
    } & EventMessagePartUpdated)
  | ({
      type: "message.part.removed"
    } & EventMessagePartRemoved)
  | ({
      type: "storage.write"
    } & EventStorageWrite)
  | ({
      type: "file.edited"
    } & EventFileEdited)
  | ({
      type: "server.connected"
    } & EventServerConnected)
  | ({
      type: "permission.updated"
    } & EventPermissionUpdated)
  | ({
      type: "permission.replied"
    } & EventPermissionReplied)
  | ({
      type: "session.updated"
    } & EventSessionUpdated)
  | ({
      type: "session.deleted"
    } & EventSessionDeleted)
  | ({
      type: "session.idle"
    } & EventSessionIdle)
  | ({
      type: "session.error"
    } & EventSessionError)
  | ({
      type: "file.watcher.updated"
    } & EventFileWatcherUpdated)
  | ({
      type: "ide.installed"
    } & EventIdeInstalled)

export type EventInstallationUpdated = {
  type: string
  properties: {
    version: string
  }
}

export type EventLspClientDiagnostics = {
  type: string
  properties: {
    serverID: string
    path: string
  }
}

export type EventMessageUpdated = {
  type: string
  properties: {
    info: Message
  }
}

export type Message =
  | ({
      role: "user"
    } & UserMessage)
  | ({
      role: "assistant"
    } & AssistantMessage)

export type UserMessage = {
  id: string
  sessionID: string
  role: string
  time: {
    created: number
  }
}

export type AssistantMessage = {
  id: string
  sessionID: string
  role: string
  time: {
    created: number
    completed?: number
  }
  error?:
    | ({
        name: "ProviderAuthError"
      } & ProviderAuthError)
    | ({
        name: "UnknownError"
      } & UnknownError)
    | ({
        name: "MessageOutputLengthError"
      } & MessageOutputLengthError)
    | ({
        name: "MessageAbortedError"
      } & MessageAbortedError)
  system: Array<string>
  modelID: string
  providerID: string
  mode: string
  path: {
    cwd: string
    root: string
  }
  summary?: boolean
  cost: number
  tokens: {
    input: number
    output: number
    reasoning: number
    cache: {
      read: number
      write: number
    }
  }
}

export type ProviderAuthError = {
  name: string
  data: {
    providerID: string
    message: string
  }
}

export type UnknownError = {
  name: string
  data: {
    message: string
  }
}

export type MessageOutputLengthError = {
  name: string
  data: {
    [key: string]: unknown
  }
}

export type MessageAbortedError = {
  name: string
  data: {
    [key: string]: unknown
  }
}

export type EventMessageRemoved = {
  type: string
  properties: {
    sessionID: string
    messageID: string
  }
}

export type EventMessagePartUpdated = {
  type: string
  properties: {
    part: Part
  }
}

export type Part =
  | ({
      type: "text"
    } & TextPart)
  | ({
      type: "file"
    } & FilePart)
  | ({
      type: "tool"
    } & ToolPart)
  | ({
      type: "step-start"
    } & StepStartPart)
  | ({
      type: "step-finish"
    } & StepFinishPart)
  | ({
      type: "snapshot"
    } & SnapshotPart)
  | ({
      type: "patch"
    } & PatchPart)
  | ({
      type: "agent"
    } & AgentPart)

export type TextPart = {
  id: string
  sessionID: string
  messageID: string
  type: string
  text: string
  synthetic?: boolean
  time?: {
    start: number
    end?: number
  }
}

export type FilePart = {
  id: string
  sessionID: string
  messageID: string
  type: string
  mime: string
  filename?: string
  url: string
  source?: FilePartSource
}

export type FilePartSource =
  | ({
      type: "file"
    } & FileSource)
  | ({
      type: "symbol"
    } & SymbolSource)

export type FileSource = {
  text: FilePartSourceText
  type: string
  path: string
}

export type FilePartSourceText = {
  value: string
  start: number
  end: number
}

export type SymbolSource = {
  text: FilePartSourceText
  type: string
  path: string
  range: Range
  name: string
  kind: number
}

export type Range = {
  start: {
    line: number
    character: number
  }
  end: {
    line: number
    character: number
  }
}

export type ToolPart = {
  id: string
  sessionID: string
  messageID: string
  type: string
  callID: string
  tool: string
  state: ToolState
}

export type ToolState =
  | ({
      status: "pending"
    } & ToolStatePending)
  | ({
      status: "running"
    } & ToolStateRunning)
  | ({
      status: "completed"
    } & ToolStateCompleted)
  | ({
      status: "error"
    } & ToolStateError)

export type ToolStatePending = {
  status: string
}

export type ToolStateRunning = {
  status: string
  input?: unknown
  title?: string
  metadata?: {
    [key: string]: unknown
  }
  time: {
    start: number
  }
}

export type ToolStateCompleted = {
  status: string
  input: {
    [key: string]: unknown
  }
  output: string
  title: string
  metadata: {
    [key: string]: unknown
  }
  time: {
    start: number
    end: number
  }
}

export type ToolStateError = {
  status: string
  input: {
    [key: string]: unknown
  }
  error: string
  time: {
    start: number
    end: number
  }
}

export type StepStartPart = {
  id: string
  sessionID: string
  messageID: string
  type: string
}

export type StepFinishPart = {
  id: string
  sessionID: string
  messageID: string
  type: string
  cost: number
  tokens: {
    input: number
    output: number
    reasoning: number
    cache: {
      read: number
      write: number
    }
  }
}

export type SnapshotPart = {
  id: string
  sessionID: string
  messageID: string
  type: string
  snapshot: string
}

export type PatchPart = {
  id: string
  sessionID: string
  messageID: string
  type: string
  hash: string
  files: Array<string>
}

export type AgentPart = {
  id: string
  sessionID: string
  messageID: string
  type: string
  name: string
  source?: {
    value: string
    start: number
    end: number
  }
}

export type EventMessagePartRemoved = {
  type: string
  properties: {
    sessionID: string
    messageID: string
    partID: string
  }
}

export type EventStorageWrite = {
  type: string
  properties: {
    key: string
    content?: unknown
  }
}

export type EventFileEdited = {
  type: string
  properties: {
    file: string
  }
}

export type EventServerConnected = {
  type: string
  properties: {
    [key: string]: unknown
  }
}

export type EventPermissionUpdated = {
  type: string
  properties: Permission
}

export type Permission = {
  id: string
  type: string
  pattern?: string
  sessionID: string
  messageID: string
  callID?: string
  title: string
  metadata: {
    [key: string]: unknown
  }
  time: {
    created: number
  }
}

export type EventPermissionReplied = {
  type: string
  properties: {
    sessionID: string
    permissionID: string
    response: string
  }
}

export type EventSessionUpdated = {
  type: string
  properties: {
    info: Session
  }
}

export type Session = {
  id: string
  parentID?: string
  share?: {
    url: string
  }
  title: string
  version: string
  time: {
    created: number
    updated: number
  }
  revert?: {
    messageID: string
    partID?: string
    snapshot?: string
    diff?: string
  }
}

export type EventSessionDeleted = {
  type: string
  properties: {
    info: Session
  }
}

export type EventSessionIdle = {
  type: string
  properties: {
    sessionID: string
  }
}

export type EventSessionError = {
  type: string
  properties: {
    sessionID?: string
    error?:
      | ({
          name: "ProviderAuthError"
        } & ProviderAuthError)
      | ({
          name: "UnknownError"
        } & UnknownError)
      | ({
          name: "MessageOutputLengthError"
        } & MessageOutputLengthError)
      | ({
          name: "MessageAbortedError"
        } & MessageAbortedError)
  }
}

export type EventFileWatcherUpdated = {
  type: string
  properties: {
    file: string
    event: string
  }
}

export type EventIdeInstalled = {
  type: string
  properties: {
    ide: string
  }
}

export type App = {
  hostname: string
  git: boolean
  path: {
    config: string
    data: string
    root: string
    cwd: string
    state: string
  }
  time: {
    initialized?: number
  }
}

export type Config = {
  /**
   * JSON schema reference for configuration validation
   */
  $schema?: string
  /**
   * Theme name to use for the interface
   */
  theme?: string
  keybinds?: KeybindsConfig
  plugin?: Array<string>
  snapshot?: boolean
  /**
   * Control sharing behavior:'manual' allows manual sharing via commands, 'auto' enables automatic sharing, 'disabled' disables all sharing
   */
  share?: "manual" | "auto" | "disabled"
  /**
   * @deprecated Use 'share' field instead. Share newly created sessions automatically
   */
  autoshare?: boolean
  /**
   * Automatically update to the latest version
   */
  autoupdate?: boolean
  /**
   * Disable providers that are loaded automatically
   */
  disabled_providers?: Array<string>
  /**
   * Model to use in the format of provider/model, eg anthropic/claude-2
   */
  model?: string
  /**
   * Small model to use for tasks like title generation in the format of provider/model
   */
  small_model?: string
  /**
   * Custom username to display in conversations instead of system username
   */
  username?: string
  /**
   * @deprecated Use `agent` field instead.
   */
  mode?: {
    build?: AgentConfig
    plan?: AgentConfig
    [key: string]: AgentConfig | undefined
  }
  /**
   * Agent configuration, see https://opencode.ai/docs/agent
   */
  agent?: {
    plan?: AgentConfig
    build?: AgentConfig
    general?: AgentConfig
    [key: string]: AgentConfig | undefined
  }
  /**
   * Custom provider configurations and model overrides
   */
  provider?: {
    [key: string]: {
      api?: string
      name?: string
      env?: Array<string>
      id?: string
      npm?: string
      models: {
        [key: string]: {
          id?: string
          name?: string
          release_date?: string
          attachment?: boolean
          reasoning?: boolean
          temperature?: boolean
          tool_call?: boolean
          cost?: {
            input: number
            output: number
            cache_read?: number
            cache_write?: number
          }
          limit?: {
            context: number
            output: number
          }
          options?: {
            [key: string]: unknown
          }
        }
      }
      options?: {
        apiKey?: string
        baseURL?: string
        [key: string]: unknown | string | undefined
      }
    }
  }
  /**
   * MCP (Model Context Protocol) server configurations
   */
  mcp?: {
    [key: string]:
      | ({
          type: "local"
        } & McpLocalConfig)
      | ({
          type: "remote"
        } & McpRemoteConfig)
  }
  formatter?: {
    [key: string]: {
      disabled?: boolean
      command?: Array<string>
      environment?: {
        [key: string]: string
      }
      extensions?: Array<string>
    }
  }
  lsp?: {
    [key: string]:
      | {
          disabled: boolean
        }
      | {
          command: Array<string>
          extensions?: Array<string>
          disabled?: boolean
          env?: {
            [key: string]: string
          }
          initialization?: {
            [key: string]: unknown
          }
        }
  }
  /**
   * Additional instruction files or patterns to include
   */
  instructions?: Array<string>
  layout?: LayoutConfig
  permission?: {
    edit?: string
    bash?:
      | string
      | {
          [key: string]: string
        }
  }
  experimental?: {
    hook?: {
      file_edited?: {
        [key: string]: Array<{
          command: Array<string>
          environment?: {
            [key: string]: string
          }
        }>
      }
      session_completed?: Array<{
        command: Array<string>
        environment?: {
          [key: string]: string
        }
      }>
    }
  }
}

export type KeybindsConfig = {
  /**
   * Leader key for keybind combinations
   */
  leader: string
  /**
   * Show help dialog
   */
  app_help: string
  /**
   * @deprecated use switch_agent. Next mode
   */
  switch_mode: string
  /**
   * @deprecated use switch_agent_reverse. Previous mode
   */
  switch_mode_reverse: string
  /**
   * Next agent
   */
  switch_agent: string
  /**
   * Previous agent
   */
  switch_agent_reverse: string
  /**
   * Open external editor
   */
  editor_open: string
  /**
   * Export session to editor
   */
  session_export: string
  /**
   * Create a new session
   */
  session_new: string
  /**
   * List all sessions
   */
  session_list: string
  /**
   * Share current session
   */
  session_share: string
  /**
   * Unshare current session
   */
  session_unshare: string
  /**
   * Interrupt current session
   */
  session_interrupt: string
  /**
   * Compact the session
   */
  session_compact: string
  /**
   * Toggle tool details
   */
  tool_details: string
  /**
   * List available models
   */
  model_list: string
  /**
   * List available themes
   */
  theme_list: string
  /**
   * List files
   */
  file_list: string
  /**
   * Close file
   */
  file_close: string
  /**
   * Search file
   */
  file_search: string
  /**
   * Split/unified diff
   */
  file_diff_toggle: string
  /**
   * Create/update AGENTS.md
   */
  project_init: string
  /**
   * Clear input field
   */
  input_clear: string
  /**
   * Paste from clipboard
   */
  input_paste: string
  /**
   * Submit input
   */
  input_submit: string
  /**
   * Insert newline in input
   */
  input_newline: string
  /**
   * Scroll messages up by one page
   */
  messages_page_up: string
  /**
   * Scroll messages down by one page
   */
  messages_page_down: string
  /**
   * Scroll messages up by half page
   */
  messages_half_page_up: string
  /**
   * Scroll messages down by half page
   */
  messages_half_page_down: string
  /**
   * Navigate to previous message
   */
  messages_previous: string
  /**
   * Navigate to next message
   */
  messages_next: string
  /**
   * Navigate to first message
   */
  messages_first: string
  /**
   * Navigate to last message
   */
  messages_last: string
  /**
   * Toggle layout
   */
  messages_layout_toggle: string
  /**
   * Copy message
   */
  messages_copy: string
  /**
   * @deprecated use messages_undo. Revert message
   */
  messages_revert: string
  /**
   * Undo message
   */
  messages_undo: string
  /**
   * Redo message
   */
  messages_redo: string
  /**
   * Exit the application
   */
  app_exit: string
}

export type AgentConfig = {
  model?: string
  temperature?: number
  top_p?: number
  prompt?: string
  tools?: {
    [key: string]: boolean
  }
  disable?: boolean
  /**
   * Description of when to use the agent
   */
  description?: string
  mode?: string
}

export type Provider = {
  api?: string
  name: string
  env: Array<string>
  id: string
  npm?: string
  models: {
    [key: string]: Model
  }
}

export type Model = {
  id: string
  name: string
  release_date: string
  attachment: boolean
  reasoning: boolean
  temperature: boolean
  tool_call: boolean
  cost: {
    input: number
    output: number
    cache_read?: number
    cache_write?: number
  }
  limit: {
    context: number
    output: number
  }
  options: {
    [key: string]: unknown
  }
}

export type McpLocalConfig = {
  /**
   * Type of MCP server connection
   */
  type: string
  /**
   * Command and arguments to run the MCP server
   */
  command: Array<string>
  /**
   * Environment variables to set when running the MCP server
   */
  environment?: {
    [key: string]: string
  }
  /**
   * Enable or disable the MCP server on startup
   */
  enabled?: boolean
}

export type McpRemoteConfig = {
  /**
   * Type of MCP server connection
   */
  type: string
  /**
   * URL of the remote MCP server
   */
  url: string
  /**
   * Enable or disable the MCP server on startup
   */
  enabled?: boolean
  /**
   * Headers to send with the request
   */
  headers?: {
    [key: string]: string
  }
}

export type LayoutConfig = "auto" | "stretch"

export type _Error = {
  data: {
    [key: string]: unknown
  }
}

export type TextPartInput = {
  id?: string
  type: string
  text: string
  synthetic?: boolean
  time?: {
    start: number
    end?: number
  }
}

export type FilePartInput = {
  id?: string
  type: string
  mime: string
  filename?: string
  url: string
  source?: FilePartSource
}

export type AgentPartInput = {
  id?: string
  type: string
  name: string
  source?: {
    value: string
    start: number
    end: number
  }
}

export type Symbol = {
  name: string
  kind: number
  location: {
    uri: string
    range: Range
  }
}

export type File = {
  path: string
  added: number
  removed: number
  status: "added" | "deleted" | "modified"
}

export type Agent = {
  name: string
  description?: string
  mode: string
  topP?: number
  temperature?: number
  model?: {
    modelID: string
    providerID: string
  }
  prompt?: string
  tools: {
    [key: string]: boolean
  }
}

export type EventSubscribeData = {
  body?: never
  path?: never
  query?: never
  url: "/event"
}

export type EventSubscribeResponses = {
  /**
   * Event stream
   */
  200: Event
}

export type EventSubscribeResponse = EventSubscribeResponses[keyof EventSubscribeResponses]

export type AppGetData = {
  body?: never
  path?: never
  query?: never
  url: "/app"
}

export type AppGetResponses = {
  /**
   * 200
   */
  200: App
}

export type AppGetResponse = AppGetResponses[keyof AppGetResponses]

export type AppInitData = {
  body?: never
  path?: never
  query?: never
  url: "/app/init"
}

export type AppInitResponses = {
  /**
   * Initialize the app
   */
  200: boolean
}

export type AppInitResponse = AppInitResponses[keyof AppInitResponses]

export type ConfigGetData = {
  body?: never
  path?: never
  query?: never
  url: "/config"
}

export type ConfigGetResponses = {
  /**
   * Get config info
   */
  200: Config
}

export type ConfigGetResponse = ConfigGetResponses[keyof ConfigGetResponses]

export type SessionListData = {
  body?: never
  path?: never
  query?: never
  url: "/session"
}

export type SessionListResponses = {
  /**
   * List of sessions
   */
  200: Array<Session>
}

export type SessionListResponse = SessionListResponses[keyof SessionListResponses]

export type SessionCreateData = {
  body?: never
  path?: never
  query?: never
  url: "/session"
}

export type SessionCreateErrors = {
  /**
   * Bad request
   */
  400: _Error
}

export type SessionCreateError = SessionCreateErrors[keyof SessionCreateErrors]

export type SessionCreateResponses = {
  /**
   * Successfully created session
   */
  200: Session
}

export type SessionCreateResponse = SessionCreateResponses[keyof SessionCreateResponses]

export type SessionDeleteData = {
  body?: never
  path: {
    id: string
  }
  query?: never
  url: "/session/{id}"
}

export type SessionDeleteResponses = {
  /**
   * Successfully deleted session
   */
  200: boolean
}

export type SessionDeleteResponse = SessionDeleteResponses[keyof SessionDeleteResponses]

export type SessionGetData = {
  body?: never
  path: {
    id: string
  }
  query?: never
  url: "/session/{id}"
}

export type SessionGetResponses = {
  /**
   * Get session
   */
  200: Session
}

export type SessionGetResponse = SessionGetResponses[keyof SessionGetResponses]

export type SessionInitData = {
  body?: {
    messageID: string
    providerID: string
    modelID: string
  }
  path: {
    /**
     * Session ID
     */
    id: string
  }
  query?: never
  url: "/session/{id}/init"
}

export type SessionInitResponses = {
  /**
   * 200
   */
  200: boolean
}

export type SessionInitResponse = SessionInitResponses[keyof SessionInitResponses]

export type SessionAbortData = {
  body?: never
  path: {
    id: string
  }
  query?: never
  url: "/session/{id}/abort"
}

export type SessionAbortResponses = {
  /**
   * Aborted session
   */
  200: boolean
}

export type SessionAbortResponse = SessionAbortResponses[keyof SessionAbortResponses]

export type SessionUnshareData = {
  body?: never
  path: {
    id: string
  }
  query?: never
  url: "/session/{id}/share"
}

export type SessionUnshareResponses = {
  /**
   * Successfully unshared session
   */
  200: Session
}

export type SessionUnshareResponse = SessionUnshareResponses[keyof SessionUnshareResponses]

export type SessionShareData = {
  body?: never
  path: {
    id: string
  }
  query?: never
  url: "/session/{id}/share"
}

export type SessionShareResponses = {
  /**
   * Successfully shared session
   */
  200: Session
}

export type SessionShareResponse = SessionShareResponses[keyof SessionShareResponses]

export type SessionSummarizeData = {
  body?: {
    providerID: string
    modelID: string
  }
  path: {
    /**
     * Session ID
     */
    id: string
  }
  query?: never
  url: "/session/{id}/summarize"
}

export type SessionSummarizeResponses = {
  /**
   * Summarized session
   */
  200: boolean
}

export type SessionSummarizeResponse = SessionSummarizeResponses[keyof SessionSummarizeResponses]

export type SessionMessagesData = {
  body?: never
  path: {
    /**
     * Session ID
     */
    id: string
  }
  query?: never
  url: "/session/{id}/message"
}

export type SessionMessagesResponses = {
  /**
   * List of messages
   */
  200: Array<{
    info: Message
    parts: Array<Part>
  }>
}

export type SessionMessagesResponse = SessionMessagesResponses[keyof SessionMessagesResponses]

export type SessionChatData = {
  body?: {
    messageID?: string
    providerID: string
    modelID: string
    agent?: string
    system?: string
    tools?: {
      [key: string]: boolean
    }
    parts: Array<
      | ({
          type: "text"
        } & TextPartInput)
      | ({
          type: "file"
        } & FilePartInput)
      | ({
          type: "agent"
        } & AgentPartInput)
    >
  }
  path: {
    /**
     * Session ID
     */
    id: string
  }
  query?: never
  url: "/session/{id}/message"
}

export type SessionChatResponses = {
  /**
   * Created message
   */
  200: AssistantMessage
}

export type SessionChatResponse = SessionChatResponses[keyof SessionChatResponses]

export type SessionMessageData = {
  body?: never
  path: {
    /**
     * Session ID
     */
    id: string
    /**
     * Message ID
     */
    messageID: string
  }
  query?: never
  url: "/session/{id}/message/{messageID}"
}

export type SessionMessageResponses = {
  /**
   * Message
   */
  200: {
    info: Message
    parts: Array<Part>
  }
}

export type SessionMessageResponse = SessionMessageResponses[keyof SessionMessageResponses]

export type SessionRevertData = {
  body?: {
    messageID: string
    partID?: string
  }
  path: {
    id: string
  }
  query?: never
  url: "/session/{id}/revert"
}

export type SessionRevertResponses = {
  /**
   * Updated session
   */
  200: Session
}

export type SessionRevertResponse = SessionRevertResponses[keyof SessionRevertResponses]

export type SessionUnrevertData = {
  body?: never
  path: {
    id: string
  }
  query?: never
  url: "/session/{id}/unrevert"
}

export type SessionUnrevertResponses = {
  /**
   * Updated session
   */
  200: Session
}

export type SessionUnrevertResponse = SessionUnrevertResponses[keyof SessionUnrevertResponses]

export type PostSessionByIdPermissionsByPermissionIdData = {
  body?: {
    response: "once" | "always" | "reject"
  }
  path: {
    id: string
    permissionID: string
  }
  query?: never
  url: "/session/{id}/permissions/{permissionID}"
}

export type PostSessionByIdPermissionsByPermissionIdResponses = {
  /**
   * Permission processed successfully
   */
  200: boolean
}

export type PostSessionByIdPermissionsByPermissionIdResponse =
  PostSessionByIdPermissionsByPermissionIdResponses[keyof PostSessionByIdPermissionsByPermissionIdResponses]

export type ConfigProvidersData = {
  body?: never
  path?: never
  query?: never
  url: "/config/providers"
}

export type ConfigProvidersResponses = {
  /**
   * List of providers
   */
  200: {
    providers: Array<Provider>
    default: {
      [key: string]: string
    }
  }
}

export type ConfigProvidersResponse = ConfigProvidersResponses[keyof ConfigProvidersResponses]

export type FindTextData = {
  body?: never
  path?: never
  query: {
    pattern: string
  }
  url: "/find"
}

export type FindTextResponses = {
  /**
   * Matches
   */
  200: Array<{
    path: {
      text: string
    }
    lines: {
      text: string
    }
    line_number: number
    absolute_offset: number
    submatches: Array<{
      match: {
        text: string
      }
      start: number
      end: number
    }>
  }>
}

export type FindTextResponse = FindTextResponses[keyof FindTextResponses]

export type FindFilesData = {
  body?: never
  path?: never
  query: {
    query: string
  }
  url: "/find/file"
}

export type FindFilesResponses = {
  /**
   * File paths
   */
  200: Array<string>
}

export type FindFilesResponse = FindFilesResponses[keyof FindFilesResponses]

export type FindSymbolsData = {
  body?: never
  path?: never
  query: {
    query: string
  }
  url: "/find/symbol"
}

export type FindSymbolsResponses = {
  /**
   * Symbols
   */
  200: Array<Symbol>
}

export type FindSymbolsResponse = FindSymbolsResponses[keyof FindSymbolsResponses]

export type FileReadData = {
  body?: never
  path?: never
  query: {
    path: string
  }
  url: "/file"
}

export type FileReadResponses = {
  /**
   * File content
   */
  200: {
    type: "raw" | "patch"
    content: string
  }
}

export type FileReadResponse = FileReadResponses[keyof FileReadResponses]

export type FileStatusData = {
  body?: never
  path?: never
  query?: never
  url: "/file/status"
}

export type FileStatusResponses = {
  /**
   * File status
   */
  200: Array<File>
}

export type FileStatusResponse = FileStatusResponses[keyof FileStatusResponses]

export type AppLogData = {
  body?: {
    /**
     * Service name for the log entry
     */
    service: string
    /**
     * Log level
     */
    level: "debug" | "info" | "error" | "warn"
    /**
     * Log message
     */
    message: string
    /**
     * Additional metadata for the log entry
     */
    extra?: {
      [key: string]: unknown
    }
  }
  path?: never
  query?: never
  url: "/log"
}

export type AppLogResponses = {
  /**
   * Log entry written successfully
   */
  200: boolean
}

export type AppLogResponse = AppLogResponses[keyof AppLogResponses]

export type AppAgentsData = {
  body?: never
  path?: never
  query?: never
  url: "/agent"
}

export type AppAgentsResponses = {
  /**
   * List of agents
   */
  200: Array<Agent>
}

export type AppAgentsResponse = AppAgentsResponses[keyof AppAgentsResponses]

export type TuiAppendPromptData = {
  body?: {
    text: string
  }
  path?: never
  query?: never
  url: "/tui/append-prompt"
}

export type TuiAppendPromptResponses = {
  /**
   * Prompt processed successfully
   */
  200: boolean
}

export type TuiAppendPromptResponse = TuiAppendPromptResponses[keyof TuiAppendPromptResponses]

export type TuiOpenHelpData = {
  body?: never
  path?: never
  query?: never
  url: "/tui/open-help"
}

export type TuiOpenHelpResponses = {
  /**
   * Help dialog opened successfully
   */
  200: boolean
}

export type TuiOpenHelpResponse = TuiOpenHelpResponses[keyof TuiOpenHelpResponses]

export type TuiOpenSessionsData = {
  body?: never
  path?: never
  query?: never
  url: "/tui/open-sessions"
}

export type TuiOpenSessionsResponses = {
  /**
   * Session dialog opened successfully
   */
  200: boolean
}

export type TuiOpenSessionsResponse = TuiOpenSessionsResponses[keyof TuiOpenSessionsResponses]

export type TuiOpenThemesData = {
  body?: never
  path?: never
  query?: never
  url: "/tui/open-themes"
}

export type TuiOpenThemesResponses = {
  /**
   * Theme dialog opened successfully
   */
  200: boolean
}

export type TuiOpenThemesResponse = TuiOpenThemesResponses[keyof TuiOpenThemesResponses]

export type TuiOpenModelsData = {
  body?: never
  path?: never
  query?: never
  url: "/tui/open-models"
}

export type TuiOpenModelsResponses = {
  /**
   * Model dialog opened successfully
   */
  200: boolean
}

export type TuiOpenModelsResponse = TuiOpenModelsResponses[keyof TuiOpenModelsResponses]

export type TuiSubmitPromptData = {
  body?: never
  path?: never
  query?: never
  url: "/tui/submit-prompt"
}

export type TuiSubmitPromptResponses = {
  /**
   * Prompt submitted successfully
   */
  200: boolean
}

export type TuiSubmitPromptResponse = TuiSubmitPromptResponses[keyof TuiSubmitPromptResponses]

export type TuiClearPromptData = {
  body?: never
  path?: never
  query?: never
  url: "/tui/clear-prompt"
}

export type TuiClearPromptResponses = {
  /**
   * Prompt cleared successfully
   */
  200: boolean
}

export type TuiClearPromptResponse = TuiClearPromptResponses[keyof TuiClearPromptResponses]

export type TuiExecuteCommandData = {
  body?: {
    command: string
  }
  path?: never
  query?: never
  url: "/tui/execute-command"
}

export type TuiExecuteCommandResponses = {
  /**
   * Command executed successfully
   */
  200: boolean
}

export type TuiExecuteCommandResponse = TuiExecuteCommandResponses[keyof TuiExecuteCommandResponses]

export type ClientOptions = {
  baseUrl: `${string}://${string}` | (string & {})
}
