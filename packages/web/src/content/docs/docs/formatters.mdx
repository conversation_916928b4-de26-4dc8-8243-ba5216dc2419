---
title: Formatters
description: opencode uses language specific formatters.
---

opencode automatically formats files after they are written or edited using language-specific formatters. This ensures that the code that is generated follows the code styles of your project.

---

## Built-in

opencode comes with several built-in formatters for popular languages and frameworks. Below is a list of the formatters, supported file extensions, and commands or config options it needs.

| Formatter      | Extensions                                                                                     | Requirements                          |
| -------------- | -------------------------------------------------------------------------------------------------------- | ------------------------------------- |
| gofmt          | .go                                                                                                      | `gofmt` command available             |
| mix            | .ex, .exs, .eex, .heex, .leex, .neex, .sface                                                             | `mix` command available               |
| prettier       | .js, .jsx, .ts, .tsx, .html, .css, .md, .json, .yaml, and [more](https://prettier.io/docs/en/index.html) | `prettier` dependency in `package.json` |
| biome          | .js, .jsx, .ts, .tsx, .html, .css, .md, .json, .yaml, and [more](https://biomejs.dev/)                   | `biome.json` config file              |
| zig            | .zig, .zon                                                                                               | `zig` command available               |
| clang-format   | .c, .cpp, .h, .hpp, .ino, and [more](https://clang.llvm.org/docs/ClangFormat.html)                       | `.clang-format` config file           |
| ktlint         | .kt, .kts                                                                                                | `ktlint` command available            |
| ruff           | .py, .pyi                                                                                                | `ruff` command available with config  |
| rubocop        | .rb, .rake, .gemspec, .ru                                                                                | `rubocop` command available           |
| standardrb     | .rb, .rake, .gemspec, .ru                                                                                | `standardrb` command available        |
| htmlbeautifier | .erb, .html.erb                                                                                          | `htmlbeautifier` command available    |

So if your project has `prettier` in your `package.json`, opencode will automatically use it.

---

## How it works

When opencode writes or edits a file, it:

1. Checks the file extension against all enabled formatters.
2. Runs the appropriate formatter command on the file.
3. Applies the formatting changes automatically.

This process happens in the background, ensuring your code styles are maintained without any manual steps.

---

## Configure

You can customize formatters through the `formatter` section in your opencode config.

```json title="opencode.json"
{
  "$schema": "https://opencode.ai/config.json",
  "formatter": { }
}
```

Each formatter configuration supports the following:

| Property      | Type     | Description                                             |
| ------------- | -------- | ------------------------------------------------------- |
| `disabled`    | boolean  | Set this to `true` to disable the formatter                  |
| `command`     | string[] | The command to run for formatting                       |
| `environment` | object   | Environment variables to set when running the formatter |
| `extensions`  | string[] | File extensions this formatter should handle            |

Let's look at some examples.

---

### Disabling formatters

To disable a specific formatter, set `disabled` to `true`:

```json title="opencode.json" {5}
{
  "$schema": "https://opencode.ai/config.json",
  "formatter": {
    "prettier": {
      "disabled": true
    }
  }
}
```

---

### Custom formatters

You can override the built-in formatters or add new ones by specifying the command, environment variables, and file extensions:

```json title="opencode.json" {4-14}
{
  "$schema": "https://opencode.ai/config.json",
  "formatter": {
    "prettier": {
      "command": ["npx", "prettier", "--write", "$FILE"],
      "environment": {
        "NODE_ENV": "development"
      },
      "extensions": [".js", ".ts", ".jsx", ".tsx"]
    },
    "custom-markdown-formatter": {
      "command": ["deno", "fmt", "$FILE"],
      "extensions": [".md"]
    }
  }
}
```

The **`$FILE` placeholder** in the command will be replaced with the path to the file being formatted.
